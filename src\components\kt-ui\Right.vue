<script setup>
import { computed } from "vue";
const props = defineProps({});
</script>

<template>
  <div
    :class="['right', 'animate__animated1  animate__bounceInRight1']"
    class="cus-bg-full"
  >
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.right {
  position: fixed;
  top: 0;
  right: 0;
  width: 2206px;
  bottom: 0;
  background-image: url("@/assets/imgs/page2/p26.png");
  z-index: 6;
  pointer-events: all;
  padding: 342px 116px 200px 298px;
  box-sizing: border-box;
}
</style>
