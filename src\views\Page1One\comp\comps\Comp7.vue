<template>
  <div>
    <PanelTitle
      v-loading="loading"
      title="视频监控"
      :element-loading-text="msg"
      class="mt-[74px] relative"
    >
      <div
        class="absolute right-[66px] top-[26px] w-[208px] h-[66px] bg-[url('@/assets/imgs/page1/p84.png')] cus-bg-full flex items-center"
      >
        <!-- 监控图标 -->
        <div
          class="w-[148px] h-[135px] bg-[url('@/assets/imgs/page1/p5.png')] cus-bg-full cursor-pointer"
          @click="pageRefData.pullVisible = !pageRefData.pullVisible"
        ></div>
        <!-- 确认图标 -->
        <div
          class="w-[98px] h-[66px] flex cus-flex-center bg-[url('@/assets/imgs/page1/p85.png')] cus-bg-full cursor-pointer"
          @click="changeSection5Handle"
        >
          <div
            class="w-[57px] h-[24px] bg-[url('@/assets/imgs/page1/p23.png')] cus-bg-full scale-110"
          ></div>
        </div>
        <div
          class="absolute top-[82px] right-0 w-[1000px] h-[820px] bg-[url('@/assets/imgs/page1/p83.png')] cus-bg-full py-[20px] px-[20px] z-10 overflow-scroll"
          :style="{
            visibility: pageRefData.pullVisible ? 'visible' : 'hidden',
          }"
        >
          <el-tree
            :data="treeData"
            :props="{
              children: 'children',
              label: 'name',
              disabled: handleDetail,
            }"
            ref="treeRef"
            @check-change="handleCheckChange"
            @current-change="currentChange"
            node-key="id"
            check-strictly
            show-checkbox
            @node-click="nodeClick"
            highlight-current
            :expand-on-click-node="false"
            :default-expand-all="true"
          >
            <template #default="{ node, data }">
              <span class="text-[40px]">{{ data.name }}</span>
              <!-- <el-select
                class="ml-[30px]"
                v-if="checkIds.includes(data.id)"
                v-model="selectedValues[data.id]"
                multiple
                @change="(val) => selectChange(val, data)"
                @focus="handleFocus(data)"
                :placeholder="`选择${data.name}摄像头`"
                size="large"
                style="width: 240px"
              >
                <el-option
                  v-for="item in selectedOptions[data.code]"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select> -->
            </template>
          </el-tree>
          <!-- <div v-for="item in pageRefData.section5" :key="item.title">
            <h3 class="text-[32px] text-[#CBDDF2FF]">{{ item.title }}</h3>
            <div class="flex flex-col pl-[52px] py-[12px] gap-y-[20px]">
              <div v-for="child in item.children" :key="child.label" class="cursor-pointer"
                @click="child.open = !child.open">
                <span type="checkbox" class="inline-block w-[32px] h-[32px] cus-bg-full"
                  :class="[child.open ? 'bg-check2' : 'bg-check1']"></span>
                <span class="ml-[16px]">{{ child.label }}</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <div class="h-[1002px] pt-[148px]">
        <div class="flex flex-wrap justify-start gap-[28px] pl-[75px]">
          <div
            v-for="i in flvs"
            :key="i.id"
            class="w-[528px] h-[326px] bg-[url('@/assets/imgs/page1/p98.png')] cus-bg-full cus-use-click"
            @click="handles.section6ClickHandle(i)"
          >
            <kt-xgplayer
              :url="i.flv"
              :autoplay="false"
              class="pointer-events-none"
            >
              <div class="text-[20px] absolute bottom-[40px] right-[20px]">
                {{ i.item.objectName }} {{ i.item.name }}
              </div>
            </kt-xgplayer>
          </div>
        </div>
        <div
          class="w-[1660px] h-[208px] cus-bg-full bg-[url('@/assets/imgs/page1/p10.png')] mx-auto -mt-[132px] relative -z-10"
        ></div>
        <div class="flex justify-center gap-[96px] mt-[12px]">
          <div
            class="pre w-[62px] h-[48px] cus-bg-full bg-[url('@/assets/imgs/page1/p52.png')]"
          ></div>
          <div
            class="next w-[62px] h-[48px] cus-bg-full bg-[url('@/assets/imgs/page1/p53.png')]"
          ></div>
        </div>
      </div>
      <Refresh @click="getVideo(true)" class="top-[35px] right-[300px]" />
    </PanelTitle>
    <Teleport to="#app-main">
      <My-Tip
        stl="p1-ct-2"
        v-model:visible="pageRefData.tips.tip2.visible"
        center
        close
        show-mask
      >
        <div
          class="w-[2076px] h-[1195px] cus-bg-full bg-[url('@/assets/imgs/page1/p118.png')] pt-[54px] px-[40px]"
        >
          <div
            class="w-[251.6px] h-[46.6px] cus-bg-full bg-[url('@/assets/imgs/page1/p119.png')] ml-[34px]"
          ></div>
          <div
            class="w-[1977px] h-[1035px] cus-bg-full mt-[54px] kt-flex pl-[20px] text-[100px]"
          >
            <kt-xgplayer :url="url" class="w-[1977px] h-[1000px]" />
          </div>
        </div>
      </My-Tip>
    </Teleport>
  </div>
</template>

<script setup>
import { getMp4Url } from "@/utils/assets";
import {
  onMounted,
  ref,
  onBeforeUnmount,
  computed,
  watch,
  nextTick,
} from "vue";
import Refresh from "@/components/my-ui/Refresh.vue";
import { getVideoMonitorData, getVideoLive } from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";
import META, { watchEvents, cancelEvents } from "@/META";
import { getTreeData } from "@/axios/apis/page1";
const videoId = ref("");
const loading = ref(false);
const selectedOptions = ref({});
const props = defineProps({
  flag: String,
});
const currentBigVideo = computed(() => {
  return META.value.cabin === "综合舱" ? getMp4Url("p-v1") : getMp4Url("p-v2");
});
const treeRef = ref(null);
const treeData = ref([]);
const selectedValues = ref({});
const code = ref("");
const videoList = ref([]);
const checkIds = ref([]);
onMounted(async () => {
  const ids = localStorage.getItem("checkIds");
  if (ids) {
    const nodes = JSON.parse(localStorage.getItem("nodes"));
    const arr = JSON.parse(ids);
    nodes.forEach((i) => {
      if (!arr.includes(i.id)) {
        arr.push(i.id);
      }
    });
    checkIds.value = arr;
  }
  const res = await getTreeData();
  treeData.value = res;
  console.log("getTreeData--->", res);
});
const selectChange = async (val, { code }) => {
  const res = await getVideoMonitorData({
    cabinCode: code,
  });
  videoList.value = res.data.filter((i) => val.includes(i.id));
};
const handleDetail = (data, node) => {
  return !data.sxt;
};
watch(code, async (val) => {
  const res = await getVideoMonitorData({
    cabinCode: val,
  });
  videoList.value = res.data;
});
const nodeClick = async (data, node) => {
  if (node.isLeaf && !data.sxt) {
    const code = data.code;
    const res = await getVideoMonitorData({
      cabinCode: code,
    });
    const arr = res.data.map((i) => ({ ...i, sxt: true }));
    data.children = arr;
    console.log("nodeClick", checkIds.value);

    nextTick(() => {
      treeRef.value.setCheckedKeys(checkIds.value);
    });
  }
};
const socket = ref(null);
watch(
  videoList,
  async (val) => {
    let ids;
    try {
      ids = videoList.value.map((i) => i.id);
      loading.value = true;
      const promises = videoList.value.map((item) => {
        return getVideoLive({
          id: item.id,
        });
      });
      const flvRes = await Promise.all(promises);
      if (flvRes.every((item) => item.code == "00000")) {
        msg.value = "请求成功";
      } else {
        msg.value = "请求失败";
      }
      flvs.value = flvRes.map((i) => {
        const str = i.data.split("/").pop().split(".")[0];
        const item = videoList.value.find((item) => item.id == str);
        return {
          flv: i.data,
          item,
        };
      });
    } catch (error) {
      msg.value = "请求失败";
    } finally {
      loading.value = false;
    }
    loading.value = false;
    socket.value = new WebSocket(
      `ws://172.22.60.11:9001/websocket/${window["_apiToken"]}?api-token=${window["_apiToken"]}`
    );
    socket.value.onerror = (err) => {
      console.log(err);
    };
    socket.value.onmessage = (e) => {};
    socket.value.onopen = () => {
      setInterval(() => {
        socket.value.send(
          JSON.stringify({
            deviceIds: ids,
            command: "video_live",
          })
        );
      }, 1000);
    };
  },
  {
    deep: true,
  }
);
const currentNode = ref();
watch(currentNode, async () => {
  const code = currentNode.value.code;
  const res = await getVideoMonitorData({
    cabinCode: code,
  });
  const videos = res.data;
  currentNode.value.children = videos;
  console.log(videos);
});
const nodeList = ref([]);
const limitNodes = ref([]);
const handleCheckChange = async (data, checked, indeterminate) => {
  const nodes = treeRef.value.getCheckedNodes();
  const set = new Set();
  const arr = [...nodes, ...preNodes.value];
  const res = arr.filter((item) => {
    if (item.id == data.id && !checked) {
      return false;
    }
    if (!set.has(item.id)) {
      set.add(item.id);
      return true;
    }
    return false;
  });
  limitNodes.value = res;
};
watch(
  () => limitNodes.value.length,
  () => {
    if (limitNodes.value.length > 6) {
      ElMessage.error({
        message: "勾选个数不大于六个",
      });
      while (limitNodes.value.length > 6) {
        limitNodes.value.pop();
      }
    }
    nextTick(() => {
      treeRef.value.setCheckedNodes(limitNodes.value);
    });
    nodeList.value = limitNodes.value;
    checkIds.value = limitNodes.value.map((i) => i.id);
  }
);
const currentChange = (data, node) => {
  console.log(data, node);
};
const handleFocus = async (data) => {
  const res = await getVideoMonitorData({
    cabinCode: data.code,
  });
  selectedOptions.value[data.code] = res.data;
  selectedValues.value[data.code] = [];
};
const pageRefData = ref({
  pullVisible: false,
  section5: [
    {
      title: "监控中心",
      children: [
        {
          label: "监控1",
          open: true,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
    {
      title: "廊内",
      children: [
        {
          label: "监控1",
          open: false,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
    {
      title: "廊外",
      children: [
        {
          label: "监控1",
          open: false,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
  ],

  tips: {
    tip2: {
      visible: false,
      detail: [],
    },
  },
});
const msg = ref("");
const url = ref("");
const handles = {
  async section6ClickHandle(item) {
    pageRefData.value.tips.tip2.visible = true;
    url.value = item.flv;
  },
};
const changeSection5Handle = () => {
  pageRefData.value.pullVisible = false;
  // const selected = pageRefData.value.section5.map(item)
  videoList.value = nodeList.value;
  localStorage.setItem("nodes", JSON.stringify(nodeList.value));
  localStorage.setItem("checkIds", JSON.stringify(checkIds.value));
};
const flvs = ref([]);
const apisMap = new Map([
  [
    "getVideoMonitorData",
    {
      api_pro: getVideoMonitorData,
      params: {
        code: "tonghenanlu",
      },
      callback(handledData) {
        console.log("getVideoMonitorData--->", handledData);
      },
    },
  ],
]);
const preNodes = ref([]);
const getVideo = async (flag) => {
  try {
    const nodes = localStorage.getItem("nodes");
    if (nodes) {
      preNodes.value = JSON.parse(nodes);
      return (videoList.value = JSON.parse(nodes));
    }
    flag && (loading.value = true);
    const res = await getVideoMonitorData({
      cabinCode: "tonghenanlu",
    });
    msg.value = code == "00000" ? "请求成功" : "请求失败";
    videoList.value = res.data.slice(0, 6);
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    loading.value = false;
  }
};
onMounted(getVideo);
// const getData = async (flag) => {
//   if (flag) {
//     loading.value = true;
//     await patchReq([...apisMap.values()]);
//     loading.value = false;
//   } else {
//     patchReq([...apisMap.values()]);
//   }
// };

const events = {
  web_cabin_change: (params) => {
    console.log("舱室切换 更新面板数据", params);
    // 切换视频
  },
  web_partition_change: (params) => {
    console.log("分区切换 更新面板数据", params);
  },
};

onMounted(() => {
  // getData();
  watchEvents(events);
});

onBeforeUnmount(() => {
  cancelEvents(events);
});
</script>

<style lang="less">
.p1-ct-2 {
  padding: 0;
  background-color: initial;

  .e-close {
    top: 20px;
  }
}
</style>
<style lang="less" scoped>
.bg-check1 {
  background-image: url("@/assets/imgs/page1/p29.png");
}

.bg-check2 {
  background-image: url("@/assets/imgs/page1/p28.png");
}

.active {
  color: rgb(104, 122, 132);
}
.el-tree {
  background: transparent !important;
  color: #cbddf2ff;
}
:deep(.el-tree-node__content) {
  align-items: center;
  height: 66px !important;
}
:deep(.el-checkbox__input) {
  width: 30px;
  height: 30px;
  .el-checkbox__inner {
    width: 100%;
    height: 100%;
  }
}
:deep(.el-tree-node__expand-icon) {
  font-size: 50px;
}
:deep(.el-checkbox__inner::after) {
  height: 14px;
  width: 6px;
  left: 10px;
  top: 6px;
}
// :deep(.el-tree--highlight-current) .el-tree-node.is-current>.el-tree-node__content {
//   background-color: rgba(42,95,125,.5) !important;
// }
</style>
