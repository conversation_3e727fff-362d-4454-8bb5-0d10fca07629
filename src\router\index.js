import { createRouter, createWebHashHistory } from "vue-router";
import META from "@/META.js";

const pages = import.meta.glob("../views/*/route.js", {
  eager: true,
  import: "default",
});
const viewComponent = import.meta.glob("../views/*/index.vue");

let defaultRoutePath = "";
const routes = Object.entries(pages).map(([path, meta]) => {
  const viewComponentKey = path.replace("route.js", "index.vue");
  path = path.replace("../views", "").replace("/route.js", "");
  const name = path.split("/").filter(Boolean);
  if (meta.default) defaultRoutePath = path;

  return {
    path,
    name,
    component: viewComponent[viewComponentKey],
    meta,
  };
});

// 首页设置,默认重定向

routes.push({
  path: "/",
  redirect: defaultRoutePath || routes[0].path,
});

routes.push({
  path: "/:pathMatch(.*)*",
  name: "not-found",
  redirect: defaultRoutePath,
});
const router = createRouter({
  routes,
  history: createWebHashHistory(),
});

router.beforeEach( (to, from) => {

  META.value.current_path = to.path;
  META.value.mode = to.meta.name || "default";
  return true;
});

export default router;
