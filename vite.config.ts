import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import removeConsole from 'vite-plugin-remove-console'

// 获取当前时间戳以用于生成唯一的输出目录名称
const timestamp = new Date().toISOString().replace(/[-:.]/g, '');
const outputDir = `dist/build_${timestamp}`;


// https://vitejs.dev/config/
export default defineConfig({
  base: "./",
  plugins: [
    vue(),
    removeConsole(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: [{ find: "@", replacement: path.resolve(__dirname, "./src") }],
  },
  server: {
    host: "0.0.0.0",
    port: 9981,
    open: true,
    // 代理
    proxy: {
      "/api": {
        // target: "http://str.nat300.top",
        target: "http://************:9001",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },"/live": {
        // target: "http://str.nat300.top",
        target: "http://************:8100",
        changeOrigin: true,
      },
    },
  },
  esbuild:{
    drop: ['console', 'debugger'],
  },
  build: {
    outDir: outputDir,
  },
});
