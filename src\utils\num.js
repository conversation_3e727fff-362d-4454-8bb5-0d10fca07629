export function formatNumber(num) {
  if (Number.isInteger(num)) {
    return Math.round(num);
  } else {
    return num.toFixed(1);
  }
}


// 保留小数后两位，不四舍五入
export function truncatedNumber(number) {
  return Math.floor(number * 100) / 100
}

/**
 *
 * @param {string|number} val 需要处理的数字
 * @param {number} max_person 最多保留几位小数
 * @returns 格式化后的数字
 */
export const standardization = (val, max_person = 2) => {
  if(!(val + '').includes('.')) return Number(val);
  const num = Number(val);
  const transformNumber = Number.isInteger(num) ? num : num.toFixed(max_person);
  if (max_person === 0 || transformNumber === 0) transformNumber;

  return Number((transformNumber + "").replace(/0*$/, ""));
};

