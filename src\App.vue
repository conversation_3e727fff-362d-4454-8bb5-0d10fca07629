<script setup>
import { useRouter } from "vue-router";
import { ref, reactive, onMounted, onBeforeMount } from "vue";
import * as echarts from "echarts";
import { useStore } from "vuex";
import dayjs from "dayjs";
import autofit from "autofit.js";
import { preloadImagesByName } from "@/utils/index";
import META, { watchEvents } from "@/META";
import { getInspectionData } from "@/axios/apis/pagewarn";
import { setToken } from "./auth";
import request from "./axios/request";
import { getCompImg, getPage1Img } from "./utils/assets";
import AlarmGlow from '@/components/my-ui/alarm-glow.vue'
const router = useRouter();
const store = useStore();
// 过期时间
const isShow = dayjs().isBefore(dayjs(store.state.time));
const count = ref(0);
const preCount = ref();
const alarm = ref(false)
const fetchData = async () => {
  try {
    const res = await getInspectionData({ size: 10, current: 1 });
    count.value = res.total;
    const isChecked = localStorage.getItem("isChecked") === "true";
    alarm.value = preCount.value < res.total && !isChecked
    if (preCount.value < res.total && isChecked) {
      META.value.show_warning = true;
    }
    preCount.value = res.total;
  } catch (e) {
    console.error("获取数据失败:", e);
  }
};

onMounted(async () => {
  autofit.init(
    {
      dw: 7680,
      dh: 3240,
      el: "#app-main",
      resize: true,
    },
    false
  ); // 可关闭控制台运行提示输出
  watchEvents({
    devices: (data) => {
      const devices  = data.devices || []
      localStorage.setItem('devices', JSON.stringify(devices))
    },
  });
  preloadImagesByName(["p64.png", "p61.png"], getCompImg);
  preloadImagesByName(["p115.png,'p120.png",'p120-1.png'], getPage1Img);
  fetchData();
  setInterval(fetchData, 30000);
});
</script>

<template>
  <div id="app-main" v-if="isShow">
    <kt-global-dialog />
    <alarm-glow :alarm="alarm">
      <ShowWarnButton @click="META.show_warning = true" :warning-count="count" />
    </alarm-glow>
    <PageWarning :count="count" v-if="META.show_warning"></PageWarning>
    <div class="bg-div"></div>
    <router-view> </router-view>
    <Top>
      <MyHeader />
    </Top>
    <Bottom>
      <MyFooter />
    </Bottom>
  </div>
  <!-- <Scene v-if="isShow" /> -->
</template>

<style lang="less" scoped>
#app-main {
  height: 100%;
  width: 100%;
  position: relative;
  top: 0;
  left: 0;
  z-index: 2;
  // background-image: url("@/assets/imgs/comp/bg.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .bg-div {
    position: absolute;
    inset: 0;
    background-image: url("@/assets/imgs/page2/p21.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
:global(#app) {
  height: 100%;
  width: 100%;
  pointer-events: none;
}
</style>
