<template>
  <div
    class="absolute top-[342px] -left-[348px] flex flex-col gap-y-[24px] family-Regular z-[99]"
  >
    <div
      v-for="item in pageRefData.labels"
      :key="item.type"
      class="w-[302px] h-[89px] cus-bg-full flex items-center text-[48px] cus-use-click relative"
      :class="[
        item.label === pageRefData.showLabel ? 'page1-p110' : 'page1-p109',
      ]"
      @click="pageRefData.cb(item)"
    >
      <span class="mr-[26px] ml-auto text-shadow1">
        {{ item.label }}
      </span>
      <div
        class="absolute right-[116%] -top-[60%] w-[379px] text-[40px] family-Regular sub-menu"
        v-if="pageRefData.showLabel === item.label"
      >
        <div
          v-for="child in item.children"
          :key="child"
          class="flex items-center pl-[16px] py-[26px]"
          @click.stop="handleClickChildMenu(child)"
        >
          <div class="w-[36px] h-[36px] leading-[48px]">
            <kt-toggle-img
              :imgs="[getImg('page1/p120-1.png'), getImg('page1/p120.png')]"
              :flag="pageRefData.childShowLabels.includes(child)"
            ></kt-toggle-img>
          </div>

          <span class="ml-[12px]">{{ child }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getImg } from "@/utils/assets";
const pageRefData = ref({
  childShowLabels: [],
  showLabel: "",
  labels: [
    {
      type: "d0",
      label: "消防设备",
      children: ["烟感探测器", "手动报警器", "风机按钮盒",],
      defatSelect: "烟感探测器",
    },
    {
      type: "d1",
      label: "安防设备",
      children: ["监控", "入侵报警","门禁"],
      defatSelect: "监控",
    },
    {
      type: "d2",
      label: "通信设备",
      children: ["通信主机", "通信副机"],
      defatSelect: "通信主机",
    },
    {
      type: "d3",
      label: "环控设备",
      children: [
        "氧含量检测仪",
        "温湿度检测仪",
        "甲烷含量检测仪",
        "硫化氢含量检测仪",
        "UPS柜",
        "爆管液位开关",
        "非消防配电柜",
        "水泵控制箱",
        "液压井盖控制箱",
        // "风机按钮盒",
        "照明按钮盒"
      ],
      defatSelect: "氧含量检测仪",
    },
  ],
  cb(item) {
    let selected = true;
    const preLabel = pageRefData.value.showLabel;
    if (preLabel === item.label) {
      selected = false;
      pageRefData.value.showLabel = "";
    } else {
      pageRefData.value.showLabel = item.label;
    }

    const childShowLabels = pageRefData.value.childShowLabels;
    if (selected) {
      childShowLabels.length = 0;
      childShowLabels.push(item.defatSelect);
    }

    const params = {
      type: "device_icon_show",
      data: {
        type: item.type,
        text: item.label == '通信设备'?'通讯设备':item.label,
        selected,
        children: childShowLabels,
      },
    };
    sendToUE5(params);
  },
});

function handleClickChildMenu(child) {  
  const childShowLabels = pageRefData.value.childShowLabels;
  if (childShowLabels.includes(child)) {
    childShowLabels.splice(childShowLabels.indexOf(child), 1);
  } else {
    childShowLabels.push(child);
  }

  const params = {
    type: "device_type_icon_show",
    data: {
      text: pageRefData.value.showLabel,
      children: childShowLabels,
    },
  };
  if(params.data.text =='通信设备') {
    params.data.text = '通讯设备'
  }  
  sendToUE5(params);
}
</script>

<style lang="less" scoped>
.page1-p109 {
  background-image: url("@/assets/imgs/page1/p109.png");
}
.page1-p110 {
  background-image: url("@/assets/imgs/page1/p110.png");
}

.text-shadow1 {
  text-shadow: 0px 4px 12px rgba(27, 24, 19, 0.31);
}

.sub-menu {
  background-color: #01263e;
  border-bottom: solid 4px #0c5788;
  border-radius: 16px;
  // box-shadow: 0px 4px  0px 1px #0C5788;
}
</style>
