<template>
  <div class="text-[40px]">
    <Comp5 flag="工单分析" />
    <Comp6 flag="能耗统计分析-当前值班详情" />
    <Comp7 flag="视频监控" />

    <DeviceSwitch />
  </div>
</template>

<script setup>
import Comp5 from "./comps/Comp5.vue";
import Comp6 from "./comps/Comp6.vue";
import Comp7 from "./comps/Comp7.vue";
import DeviceSwitch from "./comps/DeviceSwitch.vue";

const props = defineProps({
  // props
});
</script>



<style lang="less" scoped>


.warning-heigh {
  color: rgba(255, 241, 120, 1);
}
.warning-medium {
  color: rgba(131, 243, 103, 1);
}
.warning-lower {
  color: rgba(0, 209, 255, 1);
}


</style>
