import * as echarts from "echarts";

const createChartOp1 = (data) => {
    const rawData = data ?? [
        {
            name: "04-26",
            value: 120,
        },
        {
            name: "04-27",
            value: 200,
        },
        {
            name: "04-28",
            value: 150,
        },
        {
            name: "04-29",
            value: 80,
        },
        {
            name: "04-30",
            value: 70,
        },
        {
            name: "05-01",
            value: 110,
        },
        {
            name: "05-02",
            value: 130,
        },
    ];
    const dataset = {
        dimensions: ["name", "value"],
        source: rawData,
    };
    const option = {
        grid: {
            right: 0,
            bottom: 0,
            left: 0,
            top: "15%",
            containLabel: true,
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
            padding: 22,
            formatter: function (params) {
                console.log(params);
                const item = params[0]; // 取第一个系列的数据项
                return `${item.name}<br/>数量: ${item.value?.value} 次`;
            },
            backgroundColor: "rgba(27, 23, 21, 0.80)",
            textStyle: {
                fontSize: 40,
                color: 'fff',
            },
        },

        xAxis: {
            type: "category",
            // data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
            axisLabel: {
                fontSize: 28,
                color: "rgba(203, 221, 242, 0.60)",
            },
        },
        yAxis: {
            type: "value",
            name: "次",
            nameTextStyle: {
                fontSize: 28,
                color: "rgba(203, 221, 242, 0.60)",
                align: "center",
                padding: [0, 50, 10, 0],
            },
            axisLabel: {
                fontSize: 28,
                color: "rgba(203, 221, 242, 0.60)",
            },
            axisLine: {
                itemStyle: {
                    color: "rgba(203, 221, 242, 0.30)",
                },
            },
        },
        dataset,
        series: [
            {
                // data: [120, 200, 150, 80, 70, 110, 130],
                type: "bar",
                barWidth: "20px",
                itemStyle: {
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: "rgba(254, 225, 134, 1)", // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: "rgba(105, 193, 255, 0)", // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                },
            },
        ],
    };
    return option;
};
const arr = [
    {
        "date": "2025-01",
        "data": 0.0
    },
    {
        "date": "2025-02",
        "data": 0.0
    },
    {
        "date": "2025-03",
        "data": 0.0
    },
    {
        "date": "2025-04",
        "data": 0.0
    },
    {
        "date": "2025-05",
        "data": 0.0
    },
    {
        "date": "2025-06",
        "data": 0.0
    }
]

const createChartOp3 = (arr) => {
    // 在option1的基础上修改
    const option = createChartOp1(arr);
    option.yAxis.name = "kWh";
    // option.tooltip.formatter=function (params) {
    //   const item = params[0]; // 取第一个系列的数据项
    //   return `${item.name}<br/>能耗: ${item.value?.value} kWh`;
    // }
    option.series[0].itemStyle.color.colorStops[0].color = "rgba(0, 255, 255, 1)";
    option.series[0].itemStyle.color.colorStops[1].color =
        "rgba(105, 193, 255, 0)";
    return option;
};
