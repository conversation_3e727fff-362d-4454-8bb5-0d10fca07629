import axios from "axios";
import { showMessage } from "./status"; // 引入状态码文件
import { setToken } from "@/auth";

// import { ElMessage } from 'element-plus' // 引入el 提示框，这个项目里用什么组件库这里引什么

// 设置接口超时时间
const env = import.meta.env;
axios.defaults.timeout = 160000;
axios.defaults.baseURL = "";

const { MODE } = env; // 开发环境还是生产环境

if (MODE == "development") {
  //开发环境
  axios.defaults.baseURL = "/api";
} else if (MODE == "production") {
  //生产环境
  axios.defaults.baseURL = window._publicPath;
}

//http request 拦截器
axios.interceptors.request.use(
  async (config) => {
    Object.assign(
      config.headers,
      {
        "Content-Type": "application/json;charset=UTF-8", // 传参方式json
        //'Content-Type':'application/x-www-form-urlencoded',   // 传参方式表单
        // 'api-token':'9040d6cbb39a41279814fe8dc5dade59'              // 这里自定义配置，这里传的是token
        //  "api-token": window._apiToken,
        "api-token": window._apiToken,
        "access-token": window._apiToken,
      },
    );
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

//http response 拦截器
axios.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    const { response } = error;
    if (response.status === 401) {
      await setToken();
      const config = error.config;
      config.baseURL = "";

      return axios(error.config);
    }


    if (response) {
      // 请求已发出，但是不在2xx的范围
      showMessage(response.status); // 传入响应码，匹配响应码对应信息
      return Promise.reject(response.data);
    } else {
      // ElMessage.warning('网络连接异常,请稍后再试!')
    }
  }
);

// 封装 GET POST 请求并导出
export default function request(
  url = "",
  params = {},
  type = "GET",
  config = {}
) {
  return new Promise((resolve, reject) => {
    let promise;
    const handleMap = new Map([
      ["GET", () => axios({ url, params, ...config })],
      ["POST", () => axios({ method: "POST", url, data: params, ...config })],
    ]);
    const handleFn = handleMap.get(type.toUpperCase());
    promise = handleFn
      ? handleFn()
      : Promise.reject(`未定义 ${type.toUpperCase()} 请求方式的处理函数`);
    //处理返回
    promise
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        ElMessage.success({
          message: JSON.stringify(res) + 'error'
        })
        reject(err);
      });
  });
}
