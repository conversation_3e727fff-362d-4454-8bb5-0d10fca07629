/**
 * 
 * @param {*} str 
 * @param {*} length 
 * @returns 
 * 
 * 
 // 使用示例
const myStr = "这是一个非常长的字符串，我们希望省略显示中间的字符。";
const result = ellipsis(myStr, 16); // 假设我们希望结果字符串长度为16
console.log(result); // 输出 "这是一个...字符。"
 */
export function ellipsis(str, length) {
  if (typeof str !== "string" || typeof length !== "number" || length <= 3) {
    throw new Error("Invalid input");
  }

  // 如果原始字符串长度小于或等于所需长度，则直接返回原始字符串
  if (str.length <= length) {
    return str;
  }

  // 需要显示在省略号两侧的字符数量
  const margin = Math.floor((length - 3) / 2);

  // 计算字符串的开头和结尾部分
  const begin = str.substr(0, margin);
  const end = str.substr(str.length - margin);

  // 返回处理后的字符串
  return begin + "..." + end;
}
