<template>
  <div class="alarm-wrapper absolute z-10 left-[2480px] top-[378px]" :class="{ flashing: alarm }">
    <slot />
  </div>
</template>

<script setup>
defineProps({
  alarm: {
    type: Boolean,
    default: false,
  },
});
</script>

<style scoped>
.alarm-wrapper {
  display: inline-block;
}

.flashing::after {
  content: "";
  position: absolute;
  top: -12px;
  left: -12px;
  right: -12px;
  bottom: -12px;
  border-radius: 12px;
  pointer-events: none;
  box-shadow: 0 0 32px 12px red;
  animation: alarm-glow 2s infinite;
  z-index: -1;
}

@keyframes alarm-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 32px 12px red;
  }
  50% {
    opacity: 0.1;
    box-shadow: 0 0 12px 4px red;
  }
}

</style>
