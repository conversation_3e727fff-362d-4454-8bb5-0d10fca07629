import dayjs from "dayjs";
import request from "../request";
const obj = {
  NORMAL: '正常',
  WARN: '告警',
  OFFLINE: '离线',
  FAULT: '故障',
  DISABLE: '禁用'
}
export async function getDevicesData(params = {}) {
  let res = await request("/admin/device/paging", params, "POST");
  if (!res.data) {
    res.data = {
      "records": [
      ],
      "total": 0,
      "size": params.size || 10,
      "current": params.current || 1,
      "orders": [],
      "searchCount": true,
      "pages": 6
    }

  }

  const formatDate = (date) => {
    if (!date) return "";
    // 2024年6月27日
    return dayjs(date).format("YYYY年MM月DD日 ");
  };
  const { current, size } = res.data
  const tableData = res.data.records.map((item, index) => {    
    return ({
    k1: (current - 1) * size + index + 1,
    k2: item.code,
    k3: item.name,
    k4: item.name,
    k5: obj[item.deviceStatus],
    k6: "定位",
    deviceTypeId: item.deviceTypeId,
    deviceId: item.deviceId
  })
  });

  const pageInfo = {
    pages: res.data.pages,
    size: res.data.size,
    total: res.data.total,
    current: res.data.current,
  };

  const handledData = {
    tableData,
    pageInfo,
  };

  return handledData;
}