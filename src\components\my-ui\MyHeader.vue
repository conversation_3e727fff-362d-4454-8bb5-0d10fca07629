<template>
  <div
    class="my-header relative w-full h-[320px] text-white family-JinBuTi cus-flex-center"
  >
    <!-- 背景图 -->
    <div
      class="absolute w-full h-[320px] bg-[url('@/assets/imgs/page2/p42.png')] cus-bg-full bottom-0"
    ></div>
    <!-- 俩按钮 -->
    <div class="absolute flex gap-x-[2452px]" v-show="showCateMode">
      <div
        v-for="item of pageStaticData.section1"
        :key="item"
        class="w-[410px] h-[114px] cus-bg-full hover:cursor-pointer"
        :class="[
          pageRefData.section1 === item.label
            ? item.select_img
            : item.unselect_img,
        ]"
        @click="changeSection1Handle(item.label)"
      ></div>
    </div>

    <!-- 时间 和 天气 -->
    <div
      class="absolute flex bottom-0 w-full px-[116px] text-[60px] family-Regular items-end"
    >
      <!-- 时间 -->
      <div class="text-[#7FB1DF] font-semibold">{{ nowTimeStr }}</div>
      <!-- 天气 -->
      <div
        class="ml-auto flex items-center gap-x-[20px] relative"
        v-show="pageRefData.section1 === '实景模式'"
      >
        <div
          class="w-[2px] h-[76px] bg-[url('@/assets/imgs/comp/p30.png')] cus-bg-full absolute right-[152px]"
        ></div>
        <div
          v-for="item of pageRefData.section2"
          :key="item.label"
          class="w-[156px] h-[156px] cus-bg-full last:ml-[80px] hover:cursor-pointer relative"
          :class="[
            item.label ===
            (item.group == '1' ? pageRefData.section3 : pageRefData.section4)
              ? item.select_img
              : item.unselect_img,
          ]"
          :style="{
            width: item.w,
            height: item.h,
          }"
          @click="changeSection2Handle(item)"
        >
        <!-- 处理切换图片时闪动的问题 -->
          <div
            class="absolute inset-0 duration-300"
            :class="[item.select_img]"
            :style="{
              opacity:
                item.label ===
                (item.group == '1'
                  ? pageRefData.section3
                  : pageRefData.section4)
                  ? '1'
                  : '0',
            }"
          ></div>
          <div
            class="absolute inset-0 duration-300"
            :class="[item.unselect_img]"
            :style="{
              opacity:
                item.label ===
                (item.group == '1'
                  ? pageRefData.section3
                  : pageRefData.section4)
                  ? '0'
                  : '1',
            }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 导航栏 -->
    <div
      class="w-[2220px] h-[144px] flex gap-x-[80px] bg-[url('@/assets/imgs/page2/p47.png')] cus-bg-full absolute bottom-0 translate-y-full justify-center pointer-events-none"
    >
      <div
        v-for="route of navRoutes"
        :key="route.path"
        @click="navChange(route)"
        class="hover:cursor-pointer w-[356px] h-[88px] pointer-events-auto"
        :class="[
          isCurrenRoute(route) ? route.meta.selected_icon : route.meta.icon,
        ]"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { useDateFormat, useNow } from "@vueuse/core";
import { getImg } from "@/utils/assets";
import { ref, watch, onMounted, onBeforeUnmount, computed } from "vue";
import { useRouter } from "vue-router";
import META, { watchEvents, cancelEvents } from "@/META.js";

const props = defineProps({});

// 时间
const nowTimeStr = useDateFormat(useNow(), "YYYY年MM月DD日 HH:mm:ss dddd", {
  locales: "zh-CN",
});

const pageStaticData = {
  section1: [
    {
      label: "实景模式",
      select_img: "comp-p22",
      unselect_img: "comp-p29",
    },
    {
      label: "水晶模式",
      select_img: "comp-p9",
      unselect_img: "comp-p27",
    },
  ],
};

const pageRefData = ref({
  section1: "实景模式",
  section2: [
    {
      label: "晴天",
      unselect_img: "comp-p10",
      select_img: "comp-p11",
      group: "1", // 分组单选
    },
    {
      label: "多云",
      select_img: "comp-p16",
      unselect_img: "comp-p17",
      group: "1",
    },
    {
      label: "多雨",
      select_img: "comp-p14",
      unselect_img: "comp-p15",
      group: "1",
    },
    {
      label: "雪天",
      select_img: "comp-p12",
      unselect_img: "comp-p13",
      group: "1",
    },
    {
      label: "夜晚",
      select_img: "comp-p50",
      unselect_img: "comp-p47",
      w: "92px",
      h: "88px",
      group: "2",
    },
    {
      label: "白天",
      select_img: "comp-p49",
      unselect_img: "comp-p48",
      w: "110px",
      h: "88px",
      group: "2",
    },
  ],
  section3: "晴天",
  section4: "白天",
});

const changeSection1Handle = (label) => {
  // if (pageRefData.value.section1 == label) return;
  META.value.cate_mode = label;
  const params = {
    type: "mode_change",
    data: {
      text: label,
    },
  };
  sendToUE5(params);
  pageRefData.value.section1 = label;
};

const changeSection2Handle = (item) => {
  const group = item.group;
  let event = "";
  if (group === "1") {
    if (pageRefData.value.section3 === item.label) return;
    pageRefData.value.section3 = item.label;
    event = "weather_change";
  } else if (group === "2") {
    if (pageRefData.value.section4 === item.label) return;
    pageRefData.value.section4 = item.label;
    event = "day_night_change";
  }
  const params = {
    type: event,
    data: {
      text: item.label,
    },
  };
  sendToUE5(params);
};

const router = useRouter();
const rawRoutes = router.getRoutes();
const navRoutes = rawRoutes
  .filter((r) => r.meta && r.meta.show)
  .sort((a, b) => a.meta.order - b.meta.order);
const isCurrenRoute = (route) => router.currentRoute.value.path === route.path;

// 当是运行态势页面时，显示俩模式按钮
const showCateMode = computed(
  () => router.currentRoute.value.path === "/Page1One"
);

const navChange = (route) => {
  // if (isCurrenRoute(route)) return;

  if (route.path === "/Page2Two") {
    if (!META.value.partition) {
      META.bus.emit("header_change_partition", { partition: "01" });
    }
    META.value.corridor_side = "廊内";
    META.bus.emit("web_menus2_change", { status: "status2" });
  } else {
    META.bus.emit("web_menus2_change", { status: "status0" });
  }

  if (!META.value.partition) {
    META.value.partition = "01";
    META.bus.emit("header_change_partition", { partition: "01" });
  }
  const params = {
    type: "route_change",
    data: {
      text: route.meta.name,
      path: route.path,
    },
  };
  sendToUE5(params);
  router.push(route.path);
};

const events = {
  ue_weather_change: (params) => {
    pageRefData.value.section3 = params.text;
  },
  ue_day_night_change: (params) => {
    pageRefData.value.section4 = params.text;
  },
};

onMounted(() => {
  watchEvents(events);
});

onBeforeUnmount(() => {
  cancelEvents(events);
});
</script>

<style lang="less" scoped>
.my-header {
  .active {
    color: red;
  }
  .comp-p22 {
    background-image: url("@/assets/imgs/comp/p22.png");
  }
  .comp-p29 {
    background-image: url("@/assets/imgs/comp/p29.png");
  }
  .comp-p9 {
    background-image: url("@/assets/imgs/comp/p9.png");
  }
  .comp-p27 {
    background-image: url("@/assets/imgs/comp/p27.png");
  }

  .comp-p10 {
    background-image: url("@/assets/imgs/comp/p10.png");
  }
  .comp-p11 {
    background-image: url("@/assets/imgs/comp/p11.png");
  }
  .comp-p16 {
    background-image: url("@/assets/imgs/comp/p16.png");
  }
  .comp-p17 {
    background-image: url("@/assets/imgs/comp/p17.png");
  }
  .comp-p14 {
    background-image: url("@/assets/imgs/comp/p14.png");
  }
  .comp-p15 {
    background-image: url("@/assets/imgs/comp/p15.png");
  }
  .comp-p12 {
    background-image: url("@/assets/imgs/comp/p12.png");
  }
  .comp-p13 {
    background-image: url("@/assets/imgs/comp/p13.png");
  }
  .comp-p50 {
    background-image: url("@/assets/imgs/comp/p50.png");
  }
  .comp-p47 {
    background-image: url("@/assets/imgs/comp/p47.png");
  }
  .comp-p49 {
    background-image: url("@/assets/imgs/comp/p49.png");
  }
  .comp-p48 {
    background-image: url("@/assets/imgs/comp/p48.png");
  }

  .comp-p5 {
    background-image: url("@/assets/imgs/comp/p5.png");
  }
  .comp-p8 {
    background-image: url("@/assets/imgs/comp/p8.png");
  }

  .comp-p6 {
    background-image: url("@/assets/imgs/comp/p6.png");
  }
  .comp-p3 {
    background-image: url("@/assets/imgs/comp/p3.png");
  }

  .comp-p4 {
    background-image: url("@/assets/imgs/comp/p4.png");
  }
  .comp-p7 {
    background-image: url("@/assets/imgs/comp/p7.png");
  }
}
</style>
