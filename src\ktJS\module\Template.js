import { CACHE } from '../CACHE'
import { STATE } from '../STATE'
import { getHdr, getImg, getModel, getSky } from '@/utils/assets.js'
const Template = {
  publicPath: STATE.PUBLIC_PATH,
  viewState: 'orbit',
  bloomEnabled: false,
  cameras: {
    orbitCamera: {
      position: [
        STATE.initialState.position.x,
        STATE.initialState.position.y,
        STATE.initialState.position.z,
      ],
      near: 0.1,
      far: 1000000,
      fov: 60
    }
  },
  controls: {
    orbitControls: {
      autoRotate: false,
      autoRotateSpeed: 1,
      target: [
        STATE.initialState.target.x,
        STATE.initialState.target.y,
        STATE.initialState.target.z,
      ],
      // minDistance: 20,
      // maxDistance: 150,
      enableDamping: true,
      dampingFactor: 0.05,
    }
  },
  lights: {
    directionLights: [
      {
        color: 0xffffff,
        intensity: 2,
        position: [260, 480, -260],
        mapSize: [2048, 2048],
        near: 0.1,
        far: 500,
        bias: -0.0049,
        distance: 1100
      }],
    ambientLight: {
      color: 0xffffff,
      intensity: 1
    },
  },
  modelUrls: ['/model/shuichan.glb','/model/dixing.glb'],
  hdrUrls: ['/hdr/abandoned_construction_1k.hdr'],
  antiShake: false,
  // fog: {
  //   color: '#2c4027',
  //   intensity: 0.0012
  // },
  toneMapping: {
    toneMappingExposure: 1
  },
  background: {
    type: 'panorama',
    value: ['/sky/3.jpg'],
    options: {
      scale: 1,
      rotation: [0, 0, 0],
      fog: false, // 天空盒受雾影响 默认值为false
    },
  },
  enableShadow: true,
  // bloom: { //泛光参数配置
  //   // bloomStrength: 0, // 强度
  //   // threshold: 0, // 阈值
  //   // bloomRadius: 0, // 半径
  //   bloomStrength: .7, threshold: 0, bloomRadius: .1
  // },
  outlineEnabled: false, // 需要开启该参数
  // outline: {
  //   edgeStrength: 10, edgeGlow: 0, edgeThickness: 1, pulsePeriod: 1, visibleEdgeColor: "#98e10f", hiddenEdgeColor: "#98e10f"
  // },
  dofEnabled: false,
  msaa: {
    supersampling: true
  },
  gammaEnabled: true,
  stats: false,
  loadingBar: {
    show: true,
    type: 10
  }
}

export default Template