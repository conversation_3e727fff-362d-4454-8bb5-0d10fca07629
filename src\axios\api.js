import request from "./request";
import md5 from "blueimp-md5";

// 获取token
export async function getToken() {
  const appKey = "9a29b84dc00f4d09801d5d8aeec5a565",
    secureKey = "45b99563e03142428adac9c74db9e0d8";
  const timestamp = Date.now();
  const raw = appKey + timestamp + secureKey;
  const md5Str = md5(raw);
  const sign = `${appKey}.${timestamp}.${md5Str}`;
  const res = await request("/open/api/token", { sign });
  sendToUEToken(res.data)
  return res.data;
}

// 刷新token
export async function reRequestToken(token) {
  const res = await request("/open/api/token/refresh");
  window._apiToken = res.data;
  sendToUEToken(res.data)
  return res.data;
}

const sendToUEToken = async (token) => {
  const params = {
    type: "change_token",
    data: {
      text: token,
    },
  };
  sendToUE5(params);
};
