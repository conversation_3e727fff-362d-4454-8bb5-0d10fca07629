import dayjs from "dayjs";
import { mapUnit } from "../constants";
import { getCompImg, getImg } from './assets'
export function processedOptions(opts) {
  const markDisabled = (options) => {
    return options.map((opt) => {
      const newOpt = { ...opt };
      if (opt.children && opt.children.length > 0) {
        newOpt.disabled = true;
        newOpt.children = markDisabled(opt.children);
      }
      return newOpt;
    });
  };
  return markDisabled(opts);
}
// 生成最近n天日期的函数
function getRecentNDays(n) {
  const result = [];
  for (let i = 0; i < n; i++) {
    // 减去i天并格式化日期
    const date = dayjs().subtract(i, 'day').format('MM-DD');
    result.push(date);
  }
  return result;
}
function getLastSixHoursFullTimeStrings() {
  const result = [];
  const now = new Date();

  for (let i = 5; i >= 0; i--) {
    const d = new Date(now.getTime() - i * 60 * 60 * 1000);
    d.setMinutes(0, 0, 0); // 设置为整点

    const yyyy = d.getFullYear();
    const MM = String(d.getMonth() + 1).padStart(2, '0');
    const dd = String(d.getDate()).padStart(2, '0');
    const hh = String(d.getHours()).padStart(2, '0');
    const mm = String(d.getMinutes()).padStart(2, '0');
    const ss = String(d.getSeconds()).padStart(2, '0');

    result.push(`${yyyy}-${MM}-${dd} ${hh}:${mm}:${ss}`);
  }

  return result;
}
// export function generateStyledChartOptions2(data) {
//   const chartMap = {};
//   const recent15Days = getRecentNDays(10);
//   let flag;
//   let minMax = {}
//   data.forEach(deviceType => {
//     const { name, unit } = mapUnit[deviceType.deviceTypeCode]
//     const devices = deviceType.devices || [];
//     flag = deviceType.devices.every((i) => Boolean(i.recordData))
//     devices.forEach(dev => {
//       if (!dev.recordData) {
//         dev.recordData = recent15Days.map(date => {
//           return {
//             dataTime: date,
//             params: {
//               "createTime": null,
//               params: {
//                 [name]: {
//                   "value_real": 0,
//                   "value_format": `0${unit}`,
//                   "value": 0
//                 }
//               }
//             }
//           }
//         })
//       }
//     }
//     )
//     let chartName = deviceType.deviceTypeName;
//     if (deviceType.deviceTypeCode === 'TH_type_wd') chartName += '(温度)';
//     else if (deviceType.deviceTypeCode === 'TH_type_sd') chartName += '(湿度)';
//     const legendData = devices.map(dev => dev.name);
//     // 收集所有时间点，排序（只有有效设备才收集）
//     const allTimesSet = new Set();
//     devices.forEach(dev => {
//       dev.recordData.forEach(record => {
//         allTimesSet.add(record.dataTime);
//       });
//     });
//     const allTimes = Array.from(allTimesSet).sort();
//     let yAxisUnit = unit;
//     let minMax = {}
//     // 生成渐变色函数
//     function getGradientColor(index) {
//       const colors = [
//         { start: 'rgba(0, 170, 255, 0.2)', end: 'rgba(0, 170, 255, 0)', line: 'rgba(0, 170, 255, 1)' },
//         { start: 'rgba(46, 224, 85, 0.2)', end: 'rgba(46, 224, 85, 0)', line: 'rgba(46, 224, 85, 1)' },
//         { start: 'rgba(255, 165, 0, 0.2)', end: 'rgba(255, 165, 0, 0)', line: 'rgba(255, 165, 0, 1)' },
//         { start: 'rgba(255, 99, 132, 0.2)', end: 'rgba(255, 99, 132, 0)', line: 'rgba(255, 99, 132, 1)' },
//       ];
//       return colors[index % colors.length];
//     }

//     // 生成series（只有有效设备）
//     const series = devices.map((dev, idx) => {
//       const dataMap = {};
//       dev.recordData.forEach(record => {
//         const params = record.params || {};
//         let value = params[name]?.value_real ?? 0
//         dataMap[record.dataTime] = value;
//       });
//       const data = allTimes.map(time => dataMap[time] ?? null);
//       const colors = getGradientColor(idx);

//       return {
//         name: dev.name,
//         type: 'line',
//         data,
//         smooth: false,
//         showSymbol: false,
//         lineStyle: {
//           width: 4,
//          },
//       };
//     });
//     if (!flag) {
//       minMax = {
//         min: -10,
//         max: 10
//       }
//     }
//     // 如果没有有效设备，则allTimes为空，series也为空，但图表还是生成
//     chartMap[chartName] = {
//       grid: {
//         left: 0,
//         top: '25%',
//         right: '4%',
//         bottom: 0,
//         containLabel: true,
//       },
//       legend: {
//         data: legendData,
//         right: '0',
//         top: '4%',
//         textStyle: {
//           fontSize: 24,
//           color: 'rgba(203, 221, 242, 1)',
//         },
//       },
//       tooltip: {
//         trigger: 'axis',
//         textStyle: {
//           fontSize: 16
//         }
//       },
//       // xAxis: {
//       //   type: 'category',
//       //   data: allTimes,
//       //   axisLabel: {
//       //     fontSize: 28,
//       //     color: 'rgba(203, 221, 242, 0.60)',
//       //   },
//       //   axisTick: {
//       //     show: false,
//       //   },
//       // },
//       xAxis: {
//         type: 'category',
//         data: allTimes,
//         axisLabel: {
//           fontSize: 20,
//           color: 'rgba(203, 221, 242, 0.60)',
//           interval: 'auto', // 自动间隔显示标签
//           formatter: function (value) {
//             // 示例：只显示 “HH:mm” 或 “MM-DD HH:mm”
//             const date = new Date(value);
//             const hours = date.getHours().toString().padStart(2, '0');
//             const minutes = date.getMinutes().toString().padStart(2, '0');
//             return `${hours}:${minutes}`;
//           },
//           rotate: 45, // 避免标签重叠
//         },
//         axisTick: {
//           show: false,
//         },
//         boundaryGap: false, // 线条紧贴坐标轴起点
//       },
//       yAxis: {
//         type: 'value',
//         ...minMax,
//         name: yAxisUnit, // 显示在顶部
//         nameLocation: 'end',
//         nameTextStyle: {
//           color: 'rgba(203, 221, 242, 0.9)',
//           fontSize: 26,
//           padding: [0, 0, 10, 70], // 调整位置
//         },
//         splitLine: {
//           show: true,
//         },
//         axisLabel: {
//           fontSize: 26,
//           color: 'rgba(203, 221, 242, 0.60)',
//           // 不再显示单位
//           // formatter: value => `${value} ${yAxisUnit}`
//         },
//       },
//       series,
//     };
//   });

//   return chartMap;
// }



  export function generateStyledChartOptions2(data) {
    const chartMap = {};
    const times = getLastSixHoursFullTimeStrings();

    data.forEach(deviceType => {
      const { name, unit } = mapUnit[deviceType.deviceTypeCode];
      const devices = deviceType.devices || [];
      const allNull = devices.every(dev => dev.recordData == null);
      const flag = !allNull;
      if (allNull) {
        devices.forEach(dev => {
          dev.recordData = times.map(date => ({
            dataTime: date,
            params: {
              createTime: null,
              params: {
                [name]: {
                  value_real: 0,
                  value_format: `0${unit}`,
                  value: 0
                }
              }
            }
          }));
        });
      }
      let chartName = deviceType.deviceTypeName;
      if (deviceType.deviceTypeCode === 'TH_type_wd') chartName += '(温度)';
      else if (deviceType.deviceTypeCode === 'TH_type_sd') chartName += '(湿度)';
      const legendData = devices.map(dev => dev.name);
      const allTimesSet = new Set();
      devices.forEach(dev => {
        dev.recordData?.forEach(record => {
          allTimesSet.add(record.dataTime);
        });
      });
      const allTimes = Array.from(allTimesSet).sort();

      // 生成渐变色
      function getGradientColor(index) {
        const colors = [
          { start: 'rgba(0, 170, 255, 0.2)', end: 'rgba(0, 170, 255, 0)', line: 'rgba(0, 170, 255, 1)' },
          { start: 'rgba(46, 224, 85, 0.2)', end: 'rgba(46, 224, 85, 0)', line: 'rgba(46, 224, 85, 1)' },
          { start: 'rgba(255, 165, 0, 0.2)', end: 'rgba(255, 165, 0, 0)', line: 'rgba(255, 165, 0, 1)' },
          { start: 'rgba(255, 99, 132, 0.2)', end: 'rgba(255, 99, 132, 0)', line: 'rgba(255, 99, 132, 1)' }
        ];
        return colors[index % colors.length];
      }

      const series = devices.map((dev, idx) => {
        const dataMap = {};
        dev.recordData?.forEach(record => {
          const params = record.params?.[name];
          const value = params?.value_real ?? 0;
          dataMap[record.dataTime] = value;
        });
        const data = allTimes.map(time => dataMap[time] ?? null);

        return {
          name: dev.name,
          type: 'line',
          data,
          connectNulls: true,
          smooth: false,
          showSymbol: false,
          lineStyle: {
            width: 2
          }
        };
      });

      const minMax = flag ? {} : { min: -10, max: 10 };

      chartMap[chartName] = {
        grid: {
          left: '3%',
          top: '25%',
          right: '3%',
          bottom: 0,
          containLabel: true
        },
        legend: {
          data: legendData,
          right: '0',
          top: '4%',
          textStyle: {
            fontSize: 24,
            color: 'rgba(203, 221, 242, 1)'
          }
        },
        tooltip: {
          padding: 24,
          trigger: 'axis',
          backgroundColor: "rgba(27, 23, 21, 0.80)",
          textStyle: {
            fontSize: 40,
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: allTimes,
          axisLabel: {
            fontSize: 20,
            color: 'rgba(203, 221, 242, 0.60)',
            // interval: 'auto',
            // formatter(value) {
            //   const date = new Date(value);
            //   const hh = String(date.getHours()).padStart(2, '0');
            //   const mm = String(date.getMinutes()).padStart(2, '0');
            //    const ss = String(date.getSeconds()).padStart(2, '0');
            //   return `${hh}:${mm}:${ss}`;
            // },
            interval: function (index, value) {
              // 仅显示首个和每小时的整点（每隔 N 个点显示一次）
              const showCount = 5;
              const total = allTimes.length;
              const step = Math.floor(total / showCount);
              return index % step === 0;
            },
            formatter(value) {
              const date = new Date(value);
              const hh = String(date.getHours()).padStart(2, '0');
              const mm = String(date.getMinutes()).padStart(2, '0');
              return `${hh}:${mm}`;
            },
            rotate: 45
          },
          axisTick: {
            show: false
          },
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          ...minMax,
          name: unit,
          nameLocation: 'end',
          nameTextStyle: {
            color: 'rgba(203, 221, 242, 0.9)',
            fontSize: 26,
            padding: [0, 0, 10, 70]
          },
          splitLine: {
            show: true
          },
          axisLabel: {
            fontSize: 26,
            color: 'rgba(203, 221, 242, 0.60)'
          }
        },
        series
      };
    });

    return chartMap;
  }

export function preloadImagesByName(names,fn) {
  names.forEach(name => {
    const src = fn(name)    
    const exists = Array.from(document.head.querySelectorAll('link[rel="preload"]')).some(
      (el) => el .href === new URL(src, location.href).href
    )
    if (exists) return
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    document.head.appendChild(link)
  })
}
export function getJSONFromStorage(key) {
  if(key =='devices') {
    return window.devices
  }
  try {
    const raw = localStorage.getItem(key);
    return raw ? JSON.parse(raw) : [];
  } catch (e) {
    console.warn(`Failed to parse localStorage key: ${key}`, e);
    return [];
  }
}

 export function handleWaning(val) {
  const map = {
    "0": { label: "高", class: "warning-heigh warning-base" },
    "1": { label: "中", class: "warning-medium warning-base" },
    "2": { label: "低", class: "warning-lower warning-base" },
  }
  return map[val + ""]
  //  ?? {
  //   label: "低",
  //   class: "warning-lower warning-base",
  // };
}
