import { createStore } from 'vuex';
const mapCode = {
    综合舱: "zhc",
    电力舱: "dlc",
};
const store = createStore({
    state() {
        return {
            time: '2026-12-31',
            pageTwoCode: ['zhc', 1],
        }
    },
    mutations: {
        UPDATE_PageTwoCode(state, val) {
            let code = ''
            if (!["zhc", "dlc"].includes(val[0])) {
                code = mapCode[val[0]];
            } else {
                code = val[0]
            }
            let num = val[1]+''
            if (num.length > 1 && num < 10) {
                num = num.substring(1);
            }
            state.pageTwoCode = [code, num]
        },
    }
})
export default store