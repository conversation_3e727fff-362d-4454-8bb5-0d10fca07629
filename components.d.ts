/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AlarmGlow: typeof import('./src/components/my-ui/alarm-glow.vue')['default']
    Bottom: typeof import('./src/components/kt-ui/Bottom.vue')['default']
    CTable: typeof import('./src/components/kt-ui/C-Table.vue')['default']
    Dialog: typeof import('./src/components/my-ui/dialog.vue')['default']
    Echart: typeof import('./src/components/kt-ui/Echart.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    Item: typeof import('./src/components/kt-ui/Item.vue')['default']
    KtAnimeScroll: typeof import('./src/components/my-ui/kt-anime-scroll.vue')['default']
    KtGlobalDialog: typeof import('./src/components/my-ui/kt-global-dialog.vue')['default']
    KtScale: typeof import('./src/components/my-ui/kt-scale.vue')['default']
    KtToggleImg: typeof import('./src/components/my-ui/kt-toggle-img.vue')['default']
    KtXgplayer: typeof import('./src/components/my-ui/kt-xgplayer.vue')['default']
    Left: typeof import('./src/components/kt-ui/Left.vue')['default']
    Listbox: typeof import('./src/components/kt-ui/Listbox.vue')['default']
    MyBottom: typeof import('./src/components/my-ui/MyBottom.vue')['default']
    MyFooter: typeof import('./src/components/my-ui/MyFooter.vue')['default']
    MyHeader: typeof import('./src/components/my-ui/MyHeader.vue')['default']
    MySwitch: typeof import('./src/components/my-ui/MySwitch.vue')['default']
    MyTable: typeof import('./src/components/my-ui/MyTable.vue')['default']
    MyTip: typeof import('./src/components/my-ui/MyTip.vue')['default']
    MyTip2: typeof import('./src/components/my-ui/MyTip2.vue')['default']
    MyTipSectionTitle: typeof import('./src/components/my-ui/MyTipSectionTitle.vue')['default']
    PageWarning: typeof import('./src/components/my-ui/PageWarning.vue')['default']
    PanelTitle: typeof import('./src/components/my-ui/PanelTitle.vue')['default']
    Refresh: typeof import('./src/components/my-ui/Refresh.vue')['default']
    Right: typeof import('./src/components/kt-ui/Right.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShowWarnButton: typeof import('./src/components/my-ui/ShowWarnButton.vue')['default']
    Table: typeof import('./src/components/kt-ui/Table.vue')['default']
    TextScroll: typeof import('./src/components/my-ui/text-scroll.vue')['default']
    Top: typeof import('./src/components/kt-ui/Top.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
