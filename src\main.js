import {
  createApp
} from "vue";
import META from "@/META.js";
import "./assets/text/text.css";
import "./style.css";
import "./global.css";
import App from "./App.vue";
import router from "./router/index.js";
import store from "./store";
import "animate.css";
import "./tailwind.css";
import KTUI from "./components/kt-ui";
import XZUI from "./components/my-ui";
import "element-plus/dist/index.css";
// import ElementPlus from 'element-plus'
import * as directives from './utils/directives'

import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import {
  setToken
} from "./auth.js";
window.__MODE__ =
  import.meta.env.MODE;
window.sendToUE5 = (data) => {
  const METAValue = META.value
  const params = {
    ...data,
    page: METAValue.current_path,
    mode: METAValue.mode,
    cabin: METAValue.cabin,
    partition: METAValue.partition,
  };
  console.log("sendToUE", params);
  sendToUE(params);
};
(async () => {
  await setToken()
  const app = createApp(App);
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  for (const directiveName in directives) {
    app.directive(directiveName, directives[directiveName]);
  }
  app.config.warnHandler = () => { }
  // 发送数据到UE的方法 // 该方法会自动添加 mode / page
  app.use(KTUI).use(XZUI).use(store).use(router).mount("#app");
  app.use(ElementPlus, {
    locale: zhCn,
  })
})()