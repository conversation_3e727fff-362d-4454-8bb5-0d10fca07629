<script setup>
import { computed } from "vue";
const props = defineProps({});
</script>

<template>
  <div :class="['left', 'animate__animated1  animate__bounceInLeft1 ']" class="cus-bg-full">
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.left {
  position: fixed;
  top: 0;
  left: 0;
  width: 2206px;
  bottom: 0;
  pointer-events: all;
  background-image: url("@/assets/imgs/page2/p27.png");
  z-index: 11;
  padding: 342px 298px 200px 116px;
  box-sizing: border-box;
}
</style>
