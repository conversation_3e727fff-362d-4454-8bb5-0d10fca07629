<template>
  <div class="flex mt-[84px]">
    <PanelTitle
      v-loading="loading1"
      title="能耗统计分析"
      :element-loading-text="msg"
    >
      <div class="h-[1002px] w-[880px] px-[56px] pt-[126px] box-border">
        <div class="flex flex-wrap gap-x-[40px] gap-y-[64px]">
          <div
            v-for="item in pageStaticData.section2"
            :key="item.label"
            class="basis-2/5 grow flex"
          >
            <div
              class="h-[122px] w-[122px] cus-bg-full bg-[url('@/assets/imgs/page1/p76.png')]"
            ></div>
            <div
              class="h-[122px] w-[242px] cus-bg-full bg-[url('@/assets/imgs/page1/p67.png')] flex flex-col"
            >
              <div
                class="grow flex items-center ml-[12px] font-medium text-[#CBDDF2FF]"
              >
                {{ item.val }}
                <span class="text-[32px] ml-[16px]">{{ item.unit }}</span>
              </div>
              <div
                class="grow flex items-center ml-[12px] text-[36px] text-[#CBDDF2FF]"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="w-full h-[72px] cus-bg-full bg-[url('@/assets/imgs/page1/p49.png')] mt-[56px]"
        ></div>
        <div class="h-[380px] w-full">
          <Echart :option="pageRefData.option1" />
        </div>
      </div>
      <Refresh @click="getNhAnalysisList(true)" class="top-[30px] right-[100px]" />
    </PanelTitle>
    <PanelTitle
      v-loading="loading2"
      title="当前值班详情"
      :element-loading-text="msg"
      class="ml-auto"
    >
      <div class="h-[1002px] w-[880px] px-[56px] pt-[166px] box-border">
        <div
          class="w-full h-[274px] bg-[url('@/assets/imgs/page1/p68.png')] cus-bg-full px-[68px] items-center flex"
        >
          <div class="relative w-[198px] h-[198px] cus-flex-center">
            <!-- <div
                class="absolute w-[198px] h-[224px] bg-[url('@/assets/imgs/page1/p96.png')] cus-bg-full "
              ></div>
              <div
                class="bg2 absolute w-[200px] h-[200px] bg-[url('@/assets/imgs/page1/p97.png')] cus-bg-full"
              ></div> -->
            <div
              class="bg3 scale-[1.8] absolute w-[99px] h-[101px] bg-[url('@/assets/imgs/page1/p27.webp')] cus-bg-full"
            ></div>
          </div>
          <div class="flex flex-col gap-[40px] text-[40px]">
            <div
            class="w-[276px] h-[48px] bg-[url('@/assets/imgs/page1/p94.png')] cus-bg-full line-clamp-1 flex cus-flex-center ml-[78px] text-[28px] text-[#E4F3FF]"
          >
            {{ pageRefData.section4.currentName }}
          </div>
          <div
            class="w-[276px] h-[48px] bg-[url('@/assets/imgs/page1/p94.png')] cus-bg-full line-clamp-1 flex cus-flex-center ml-[78px] text-[28px] text-[#E4F3FF]"
          >
            {{ pageRefData.section4.currentPhone }}
          </div>
          </div>
        </div>
        <div class="w-full mt-[32px]">
          <C-Table
            :titles="pageStaticData.section4.titles"
            :table-data="pageRefData.section4.tableData"
            max-height="410px"
            gap="10px"
            :autoScroll="pageRefData.section4.tableData.length > 4"
          >
            <template #k1="{ val }">
              <div class="w-[180px] truncate">{{ val }}</div>
            </template>
          </C-Table>
        </div>
      </div>
      <Refresh @click="getData('right')" class="top-[34px] right-[100px]" />
    </PanelTitle>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, markRaw, onUnmounted } from "vue";
import { createChartOp3 } from "../chart-options/chart1";
import dayjs from "dayjs";
import { watchEvents, cancelEvents } from "@/META";
import pollingManager from '@/utils/pollingManager'
import {
  inquireThePersonOnDutyThatDay,
  getNhAnalysis,
} from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";
import Refresh from "@/components/my-ui/Refresh.vue";
const props = defineProps({
  flag: String,
});
const loading1 = ref(false);
const loading2 = ref(false);
const msg = ref("");
const pageStaticData = {
  section2: [
    {
      label: "今日用电量",
      val: "1000",
      unit: "kW",
    },
    {
      label: "本周用电量",
      val: "1000",
      unit: "kW",
    },
    {
      label: "本月用电量",
      val: "1000",
      unit: "kW",
    },
    {
      label: "本年用电量",
      val: "1000",
      unit: "kW",
    },
  ],
  section4: {
    titles: [
      {
        label: "姓名",
        prop: "k1",
        width: 1,
      },
      {
        label: "分组",
        prop: "k2",
      },
      {
        label: "班次",
        prop: "k3",
        width: 0.8,
      },
      {
        label: "联系电话",
        prop: "k4",
        width: 2.2,
      },
    ],
  },
  section6: new Array(22).fill(0),
};

const pageRefData = ref({
  option1: {},
  section4: {
    currentName: "张三",
    phone: '',
    tableData: [],
  },
});

const apisMap = new Map([
  [
    "inquireThePersonOnDutyThatDay",
    {
      api_pro: inquireThePersonOnDutyThatDay,
      params: {
        current: 1,
        size: 10,
        query: {
          endTime: dayjs().format("YYYY-MM-DD"),
          startTime: dayjs().format("YYYY-MM-DD"),
        },
      },
      callback(res) {
        const { handledData, currentName,currentPhone, code } = res;
        msg.value = code == "00000" ? "请求成功" : "请求失败";
        const section4 = pageRefData.value.section4;
        section4.tableData = handledData;
        section4.currentName = currentName;
        section4.currentPhone = currentPhone
      },
    },
  ],
]);

const getData = async (type) => {
  const obj = {
    left: loading1,
    right: loading2,
  };
  const loading = obj[type] || {};
  pageRefData.value.option1 = markRaw(createChartOp3());
  loading.value = true;
  await patchReq([...apisMap.values()]);
  loading.value = false;
};

const events = {
  web_cabin_change: (params) => {
    console.log("舱室切换 更新面板数据", params);
    // 切换视频
  },
  web_partition_change: (params) => {
    console.log("分区切换 更新面板数据", params);
  },
};
const getNhAnalysisList = async (flag) => {
 try {
 flag && (loading1.value = true)
   const res = await getNhAnalysis();
  const { weekSum, yearSum, daySum, barChart, monthSum } = res.data;
  console.log(weekSum, yearSum, daySum, barChart);
  const arr = barChart.map((item) => {
    return {
      name: item.date,
      value: item.data,
    };
  });
  pageStaticData.section2[0].val = daySum;
  pageStaticData.section2[2].val = monthSum ?? 0;
  pageStaticData.section2[1].val = weekSum;
  pageStaticData.section2[3].val = res.data.yearSum;
  pageRefData.value.option1 = markRaw(createChartOp3(arr));
 } catch (error) {
  msg.value = '请求失败'
 }finally {
  loading1.value = false
 }
};
onMounted(getNhAnalysisList);
const pollingTask1 = () => getNhAnalysisList(true)
const pollingTask2 = () => getData('right')
onMounted(() => {
  getData();
  watchEvents(events);
   pollingManager.unregister(pollingTask1)
  pollingManager.unregister(pollingTask2)
 pollingManager.register(pollingTask1)
  pollingManager.register(pollingTask2)
});
onUnmounted(() => {
  pollingManager.unregister(pollingTask1)
  pollingManager.unregister(pollingTask2)

})
onBeforeUnmount(() => {
  cancelEvents(events);
});
</script>

<style lang="less" scoped>
// :deep(.c-table) {
//   .c-table-head {
//     color: rgba(203, 221, 242, 1);
//     background-image: initial;
//   }
//   .c-table-row {
//     color: rgba(203, 221, 242, 1);
//     background-image: initial;
//   }
// }

:deep(.c-table) {
  .c-table-head {
    height: 80px;
    background-image: url("@/assets/imgs/page1/p70.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    color: rgba(148, 220, 239, 1);
  }
  .c-table-row {
    height: 84px;
    background-image: url("@/assets/imgs/page1/p78.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    color: rgba(203, 221, 242, 1);
  }
}
</style>
