<template>
  <div
    class="w-full h-full absolute cus-bg-full bg-[url('@/assets/imgs/page3/p66.png')]"
  >
    <div
      class="absolute cus-bg-full"
      :class="[currentStyle.i_class]"
      :style="{
        ...currentStyle.p_Object,
      }"
    ></div>
    <div
      v-for="(item, index) in arr"
      :class="item.class"
      style="pointer-events: auto;"
      @click="handleClick(index + 1)"
    ></div>
  </div>
</template>

<script setup>
import { ref, onUnmounted, computed, onMounted } from "vue";
import META, { watchEvents, cancelEvents } from "@/META";
import { useStore } from "vuex";
const props = defineProps({});
const bgSize = {
  width: 7680,
  height: 3240,
};
const store = useStore();
const mapCode = {
  zhc: "综合舱",
  dlc: "电力舱",
};
const handleClick = (index) => {
  let code = store.state.pageTwoCode[0];
  store.commit("UPDATE_PageTwoCode", [code, index + ""]);
  META.value.partition = index + "";
};
const arr = [
  {
    class:
      "absolute w-[220px] rotate-[20deg] h-[140px] left-[5040px] top-[1552px]",
  },
  {
    class:
      "absolute w-[168px] rotate-[21deg] h-[100px] left-[4850px] top-[1517px]",
  },
  {
    class:
      "absolute w-[150px] rotate-[20deg] h-[100px] left-[4680px] top-[1486px]",
  },
  {
    class:
      "absolute w-[155px] rotate-[20deg] h-[120px] left-[4520px] top-[1453px] ",
  },
  {
    class:
      "absolute w-[150px] rotate-[15deg] h-[100px] left-[4365px] top-[1425px] ",
  },
  {
    class:
      "absolute w-[150px] rotate-[15deg] h-[100px] left-[4193px] top-[1396px]",
  },
  {
    class:
      "absolute w-[150px] rotate-[15deg] h-[100px] left-[4036px] top-[1370px]",
  },
  {
    class:
      "absolute w-[140px] rotate-[15deg] h-[100px] left-[3890px] top-[1336px]",
  },
  {
    class:
      "absolute w-[150px] rotate-[-5deg] h-[100px] left-[3720px] top-[1342px]",
  },
  {
    class:
      "absolute w-[180px] rotate-[-5deg] h-[100px] left-[3534px] top-[1342px]",
  },
  {
    class:
      "absolute w-[170px] rotate-[-20deg] h-[100px] left-[3346px] top-[1352px] ",
  },
  {
    class:
      "absolute w-[200px] rotate-[-15deg] h-[100px] left-[3138px] top-[1399px]",
  },
  {
    class:
      "absolute w-[190px] rotate-[15deg] h-[100px] left-[2926px] top-[1394px]",
  },
  {
    class:
      "absolute w-[180px] rotate-[15deg] h-[100px] left-[2732px] top-[1355px]",
  },
  {
    class:
      "absolute w-[180px] rotate-[15deg] h-[100px] left-[2548px] top-[1320px]",
  },
];
const partitionPositions = new Map([
  [
    "c1-p1",
    {
      x: "4967px",
      y: "1552px",
      // left: "64.1797%",
      // top: "52.5309%",
    },
  ],
  [
    "c1-p2",
    {
      x: "4789px",
      y: "1517px",
    },
  ],
  [
    "c1-p3",
    {
      x: "4629px",
      y: "1486px",
    },
  ],
  [
    "c1-p4",
    {
      x: "4462px",
      y: "1453px",
    },
  ],
  [
    "c1-p5",
    {
      x: "4305px",
      y: "1425px",
    },
  ],
  [
    "c1-p6",
    {
      x: "4163px",
      y: "1396px",
    },
  ],
  [
    "c1-p7",
    {
      x: "3976px",
      y: "1370px",
    },
  ],
  [
    "c1-p8",
    {
      x: "3848px",
      y: "1336px",
    },
  ],
  [
    "c1-p9",
    {
      x: "3679px",
      y: "1337px",
    },
  ],
  [
    "c1-p10",
    {
      x: "3474px",
      y: "1342px",
    },
  ],
  [
    "c1-p11",
    {
      x: "3286px",
      y: "1356px",
    },
  ],
  [
    "c1-p12",
    {
      x: "3088px",
      y: "1399px",
    },
  ],
  [
    "c1-p13",
    {
      x: "2886px",
      y: "1394px",
    },
  ],
  [
    "c1-p14",
    {
      x: "2692px",
      y: "1355px",
    },
  ],
  [
    "c1-p15",
    {
      x: "2518px",
      y: "1320px",
    },
  ],
  [
    "c2-p1",
    {
      x: "4967px",
      y: "1552px",
    },
  ],
  [
    "c2-p2",
    {
      x: "4789px",
      y: "1517px",
    },
  ],
  [
    "c2-p3",
    {
      x: "4629px",
      y: "1486px",
    },
  ],
  [
    "c2-p4",
    {
      x: "4462px",
      y: "1453px",
    },
  ],
  [
    "c2-p5",
    {
      x: "4305px",
      y: "1425px",
    },
  ],
  [
    "c2-p6",
    {
      x: "4163px",
      y: "1396px",
    },
  ],
  [
    "c2-p7",
    {
      x: "3976px",
      y: "1370px",
    },
  ],
  [
    "c2-p8",
    {
      x: "3848px",
      y: "1336px",
    },
  ],
  [
    "c2-p9",
    {
      x: "3679px",
      y: "1337px",
    },
  ],
  [
    "c2-p10",
    {
      x: "3474px",
      y: "1342px",
    },
  ],
  [
    "c2-p11",
    {
      x: "3286px",
      y: "1356px",
    },
  ],
  [
    "c2-p12",
    {
      x: "3088px",
      y: "1399px",
    },
  ],
  [
    "c2-p13",
    {
      x: "2886px",
      y: "1394px",
    },
  ],
  [
    "c2-p14",
    {
      x: "2692px",
      y: "1355px",
    },
  ],
  [
    "c2-p15",
    {
      x: "2518px",
      y: "1320px",
    },
  ],
]);

const currentCabin = ref("综合舱");
const currentPartition = computed(() => META.value.partition);

const currentStyle = computed(() => {
  const cMap = new Map([
    ["综合舱", "c1"],
    ["电力舱", "c2"],
  ]);

  const c = cMap.get(currentCabin.value);
  const p = parseInt(currentPartition.value) || 1;
  const i_class = `${c}-p${p}`;
  const p_Object = partitionPositions.get(i_class) || {};
  return {
    i_class,
    p_Object: {
      left: (parseFloat(p_Object.x) / bgSize.width).toFixed(6) * 100 + "%",
      top: (parseFloat(p_Object.y) / bgSize.height).toFixed(6) * 100 + "%",
    },
  };
});

const events = {
  web_partition_change: (payload) => {
    const { partition } = payload;
    currentPartition.value = partition;
  },
  web_cabin_change: (payload) => {
    const { cabin } = payload;
    currentCabin.value = cabin;
  },
};

watchEvents(events);

onUnmounted(() => {
  cancelEvents(events);
});
</script>

<style lang="less" scoped>
.c1-p1 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 246px;
  height: 155px;
}
.c1-p2 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 204px;
  height: 123px;
}
.c1-p3 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 183px;
  height: 117px;
}
.c1-p4 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 187px;
  height: 119px;
}
.c1-p5 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 182px;
  height: 114px;
}
.c1-p6 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 167px;
  height: 111px;
}
.c1-p7 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 210px;
  height: 122px;
}
.c1-p8 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 173px;
  height: 109px;
}
.c1-p9 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 171px;
  height: 92px;
}
.c1-p10 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 214px;
  height: 98px;
}
.c1-p11 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 209px;
  height: 127px;
}
.c1-p12 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 224px;
  height: 113px;
}
.c1-p13 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 217px;
  height: 115px;
}
.c1-p14 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 232px;
  height: 119px;
}
.c1-p15 {
  background-image: url("@/assets/imgs/page3/c1/<EMAIL>");
  width: 214px;
  height: 113px;
}

.c2-p1 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 246px;
  height: 155px;
}
.c2-p2 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 204px;
  height: 123px;
}

.c2-p3 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 182px;
  height: 117px;
}
.c2-p4 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 187px;
  height: 119px;
}
.c2-p5 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 182px;
  height: 114px;
}
.c2-p6 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 167px;
  height: 111px;
}
.c2-p7 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 210px;
  height: 122px;
}
.c2-p8 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 173px;
  height: 109px;
}
.c2-p9 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 171px;
  height: 92px;
}
.c2-p10 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 214px;
  height: 98px;
}
.c2-p11 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 209px;
  height: 127px;
}
.c2-p12 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 224px;
  height: 115px;
}
.c2-p13 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 217px;
  height: 132px;
}
.c2-p14 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 232px;
  height: 119px;
}
.c2-p15 {
  background-image: url("@/assets/imgs/page3/c2/<EMAIL>");
  width: 214px;
  height: 113px;
}
</style>
