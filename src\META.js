import { ref } from "vue";
import mitt from "mitt";

const meta = ref({
  current_path: "/",
  mode: "运行态势",
  cabin: "综合舱",
  partition: "",
  cate_mode: "实景模式",
  corridor_side: "廊内",
  show_warning: false,
  waring_count: 0,
});
window._meta = meta;
window._bus = meta.bus = mitt();
const META = (window.META = meta);

export const watchEvents = (events) => {
  if (META.bus) {
    for (let key in events) {
      META.bus.on(key, events[key]);
    }
  }
};

export const cancelEvents = (events) => {
  if (META.bus) {
    for (let key in events) {
      META.bus.off(key, events[key]);
    }
  }
};

export default meta;
