/**
 * 批量请求并处理结果
 * @param {*} apis
 */
// export default async function patchReq(apis = []) {
//   apis.forEach((api) => {
//     const params = api.params || {};
//     api
//       .api_pro(params)
//       .then((res) => {
//         const callback = api.callback || console.log;
//         callback(res);
//       })
//       .catch((err) => {
//         console.error(api.api_pro, err);
//       });
//   });
// }
export default async function patchReq(apis = []) {
  return Promise.all(
    apis.map(async (api) => {
      const params = api.params || {};
      try {
        const res = await api.api_pro(params);
        const callback = api.callback || console.log;
        callback(res);
        return res
      } catch (err) {
        console.error(api.api_pro, err);
      }
    })
  );
}

/**
const apis = [
  {
    api_pro: page1Api.allStore,
    params:{},
    callback(res) {
      console.log(res);
    },
  },
];

patchReq(apis);
 */