<template>
  <div class="fixed inset-0 z-20 pointer-events-auto">
    <div
      class="absolute inset-0 warning cus-bg-full bg-[url('@/assets/imgs/comp/p57.png')] opacity-liner-ani"
      v-show="compRefData.showAroundWarning"
    ></div>
    <div class="absolute inset-0 z-[10] flex items-center flex-col">
      <div class="pt-[504px]">
        <div class="flex flex-col gap-y-[52px]">
          <!-- <div
            v-for="warningItem in compRefData.warningData"
            :key="warningItem.id"
            class="w-[2256px] h-[144px] truncate cus-bg-full bg-[url('@/assets/imgs/comp/p59.png')] cus-flex-center text-[64px] text-white tracking-[6px] cus-use-click"
            @click="opts.handleWarningItemClick(warningItem)"
          >
            <span>{{ warningItem.warningMsg }}</span>
          </div> -->
          <My-Tip
            stl="cs-t1 top-[854px]"
            v-model:visible="pageRefData.tip.visible"
            show-mask
            close
            center
            :is-close="false"
            :auto-close="false"
            @close-tip="handleClose"
          >
            <div
              class="ct4 w-[2768px] h-[1498px] pt-[150px] px-[50px] cus-bg-full bg-[url('@/assets/imgs/comp/p64.png')] ccc"
            >
              <div class="text-[68px] left-[5%] absolute top-[10px]">
                告警列表
              </div>
              <C-Table
                class="ct4 c-table"
                :titles="pageRefData.tip.titles"
                :table-data="pageRefData.tip.tableData"
                @item-click="onItemClick"
                max-height="1070px"
                gap="28px"
              >
                <template #k5="{ row }">
                  <span
                    v-if="row.k5 !== null && row.k5 !== undefined"
                    class="inline-block w-[48px] h-[48px] cus-flex-center"
                    :class="handleWaning(row.k5)?.class"
                  >
                    {{ handleWaning(row.k5)?.label }}
                  </span>
                  <span v-else>-</span>
                </template>
              </C-Table>

              <div class="flex justify-center gap-[80px]">
                <el-pagination
                  v-model:current-page="paginationConfig.currentPage"
                  v-model:page-size="paginationConfig.pageSize"
                  :page-sizes="paginationConfig.pageSizes"
                  size="large"
                  :disabled="false"
                  :background="false"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationConfig.total"
                  @size-change="sizeChange"
                  @current-change="currentChange"
                  class="scale-[2.6] mt-[80px] ml-[400px]"
                  locale="zh-cn"
                />
                <el-checkbox
                  class="mt-[80px] ml-[600px]"
                  v-model="isChecked"
                  label="告警模式"
                  size="large"
                />
              </div>
              <div
                class="w-[1138px] h-[436px] cus-bg-full bg-[url('@/assets/imgs/comp/p61.png')] absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-70%] mt-[112px] px-[194px] pt-[52px]"
                v-show="compRefData.showCloseConfirm"
              >
                <div class="flex items-center">
                  <svg
                    t="1720162498472"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="1993"
                    width="96"
                    height="96"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                      p-id="1994"
                      fill="#ffffff"
                    ></path>
                    <path
                      d="M512 688m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z"
                      p-id="1995"
                      fill="#ffffff"
                    ></path>
                    <path
                      d="M488 576h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
                      p-id="1996"
                      fill="#ffffff"
                    ></path>
                  </svg>
                  <span
                    class="ml-[20px] text-[64px] text-white tracking-[6px] font-medium"
                    >请确认是否关闭报警</span
                  >
                </div>
                <div
                  class="tracking-[4px] text-[52px] flex justify-between text-white mt-[140px]"
                >
                  <div
                    class="w-[236px] h-[88px] cus-bg-full bg-[url('@/assets/imgs/comp/p62.png')] cus-flex-center cus-use-click"
                    @click="opts.confirmOptHandle('cancel')"
                  >
                    取消
                  </div>
                  <div
                    class="w-[236px] h-[88px] cus-bg-full bg-[url('@/assets/imgs/comp/p63.png')] cus-flex-center cus-use-click"
                    @click="opts.confirmOptHandle('confirm')"
                  >
                    确认
                  </div>
                </div>
              </div>
            </div>
          </My-Tip>
        </div>
        <!-- <div class="flex justify-center mt-[40px]">
          <div
            class="w-[928px] h-[184px] cus-flex-center text-[64px] text-white tracking-[6px] cus-bg-full bg-[url('@/assets/imgs/comp/p60.png')] cus-use-click"
            @click="opts.handleCloseWarning"
          >
            <span>关闭报警通知</span>
          </div>
        </div> -->
      </div>
    </div>
    <My-Tip
      stl="cs-t1 top-[930px]"
      class="z-[30]"
      v-model:visible="compRefData.showTip"
      close
      center
      :auto-close="false"
      @close-tip="opts.handleDetailClose"
    >
      <div
        class="w-[2076px] text-[48px] h-[1324px] cus-bg-full bg-[url('@/assets/imgs/comp/p64.png')]"
        v-if="compRefData.currentWarningItem"
      >
        <MyDialog
          v-show="show"
          @confirm="confirm"
          @cancel="show = false"
          title="确认标为未读吗?"
          style="z-index: 1000; position: fixed"
          class="left-[50%] top-[43%] translate-x-[-50%]"
        />
        <My-Tip2
          class="left-[50%] top-[43%] translate-x-[-50%]"
          stl="t3"
          style="z-index: 1000; position: fixed"
          title_class="tt3"
          v-model:visible="visible"
        >
          <div>
            <div class="mx-[18px]">
              <textarea
                v-model="content"
                class="w-full h-[310px] outline-none bg-transparent bg-gradient-to-b from-[#1482CD66] to-[#1685CD33]"
              ></textarea>
            </div>
            <div class="flex mt-[24px] items-center mr-[20px]">
              <div
                v-for="item in options"
                :key="item"
                class="cus-use-click cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center first:ml-auto last:ml-[24px]"
                @click="() => item.cb?.()"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
        </My-Tip2>
        <div
        v-if="$route.path == '/Page1One'"
          class="absolute right-[180px] top-[66px] tracking-[1px] py-[2px] px-[10px] bg-gradient-to-r from-[#8c2020] to-pink-500 rounded-xl cursor-pointer"
        >
          <el-tooltip
            v-if="compRefData?.currentWarningItem?.status == 'no'"
            content="当前设备不能定位"
            placement="top"
            effect="dark"
            teleported
          >
            <div :style="{ cursor: 'not-allowed', opacity: 0.5 }">定位</div>
          </el-tooltip>
          <div v-else :style="{ cursor: 'pointer' }">
            <span @click="opts.locationDevice">定位</span>
          </div>
        </div>
        <div class="font-medium pt-[20px] pl-[50px]">
          <div
            class="w-[320px] h-[104px] cus-bg-full bg-[url('@/assets/imgs/comp/p65.png')]"
          ></div>
        </div>
        <div class="pl-[76px] pr-[52px] mt-[44px]">
          <div
            class="bg-[rgba(255,255,255,0.1)] px-[30px] py-[24px] text-wrap text-[44px] tracking-[2px] font-medium mx-h-[334px] whitespace-break-spaces"
          >
            <div><span>标题：</span>{{ curDetailInfo.title }}</div>
            <div><span>内容：</span>{{ curDetailInfo.content }}</div>
            <div class="flex items-center justify-start">
              <div class="w-[33%]">
                <span>告警等级：</span
                ><span>
                  {{
                    (curDetailInfo.alarmLevel !== null &&
                      curDetailInfo.alarmLevel !== undefined &&
                      handleWaning(curDetailInfo.alarmLevel).label) ||
                    "_"
                  }}
                </span>
              </div>
              <div class="w-[33%]">
                <span>告警分类：</span>{{ curDetailInfo.alarmClassify || "_" }}
              </div>
              <div class="w-[33%]">
                <span>处理状态：</span>{{ curDetailInfo.handleStatus || "_" }}
              </div>
              <!-- <div><span>处理结论：</span>{{ curDetailInfo.resultText }}</div>
              <div><span>消息来源：</span>{{ curDetailInfo.source }}</div> -->
            </div>
            <div class="flex items-center justify-start">
              <div class="w-[33%]">
                <span>处理结论：</span>{{ curDetailInfo.resultText || "_" }}
              </div>
              <div class="w-[33%]">
                <span>消息来源：</span>{{ curDetailInfo.source || "_" }}
              </div>
               <div class="w-[33%]">
                <span>图片：</span><span @click="handleImg(curDetailInfo.imgUrl)">{{ curDetailInfo.imgUrl ? '查看告警图片' : '_' }}</span>
              </div>
            </div>
          </div>
          <div
            class="mt-[48px] flex justify-between h-[550px] overflow-y-scroll flex-wrap"
          >
            <div
              v-for="i in flvs"
              :key="i"
              @click="handlePlayerClick(i.flv)"
              class="w-[948px] h-[534px] border-[pink] flex-1 cus-bg-full"
            >
              <kt-xgplayer
                class="pointer-events-none"
                :url="i.flv"
                :autoplay="true"
              >
                <div class="text-[30px] absolute bottom-[80px] right-[20px]">
                  {{ i.item.objectName }} {{ i.item.name }}
                </div>
              </kt-xgplayer>
            </div>
          </div>
          <div class="flex justify-start gap-20 mt-[50px]">
            <div
              class="bg-gradient-to-r from-[#8c2020] to-pink-500 rounded-xl relative"
              @click="unread"
            >
              标为未读
            </div>
            <div
              @click="handle"
              class="bg-gradient-to-r from-[#8c2020] to-pink-500 rounded-xl p-[6px]"
            >
              处理
            </div>
          </div>
        </div>
      </div>
    </My-Tip>
    <Teleport to="#app-main">
      <My-Tip stl="p1-ct-2" v-model:visible="showPlayer" center close show-mask>
        <div
          class="w-[2076px] h-[1195px] cus-bg-full bg-[url('@/assets/imgs/page1/p118.png')] pt-[54px] px-[40px]"
        >
          <div
            class="w-[251.6px] h-[46.6px] cus-bg-full bg-[url('@/assets/imgs/page1/p119.png')] ml-[34px]"
          ></div>
          <div
            class="w-[1977px] h-[1035px] cus-bg-full mt-[54px] kt-flex pl-[20px] text-[100px]"
          >
            <kt-xgplayer :url="videoUrl" class="w-[1977px] h-[1000px]" />
          </div>
        </div>
      </My-Tip>
    </Teleport>
    <el-dialog v-model="dialogVisible" width="40%" title="告警图片">
    <div class="flex justify-center my-[0px]">
      <el-image class="w-full" style="margin: auto" :src="imgUrl" />
    </div>
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, markRaw, computed, watch } from "vue";
import { insertZhjkAlarmReport } from "@/axios/apis/page2";
import { dayjs, ElMessage } from "element-plus";
import MyDialog from "./dialog.vue";
import META from "@/META";
import { getInspectionData, updateIsReadAlarm } from "@/axios/apis/pagewarn";
import patchReq from "@/utils/patchReq";
import { getVideoLive, getVideoMonitorData } from "@/axios/apis/page1";
import request from "@/axios/request";
import { getJSONFromStorage, handleWaning } from "@/utils";
const prop = defineProps({
  count: [String, Number],
});
const visible = ref(false);
const content = ref("");
const compRefData = ref({
  showAroundWarning: true,
  showCloseConfirm: false,
  showTip: false,
  warningData: markRaw([]),
  currentWarningItem: null,
});
const dialogVisible = ref(false)
const imgUrl = ref("");
const handleImg = (item) => {
  console.log(item, '---------------aa');
  if (!item) return;
  imgUrl.value = item;
  dialogVisible.value = true;
};
const show = ref(false);
const showPlayer = ref(false);
const handle = () => {
  visible.value = true;
  show.value = false;
};
const options = [
  {
    label: "取消",
    cb() {
      visible.value = false;
    },
  },
  {
    label: "确认",
    cb() {
      const cid = compRefData.value.currentWarningItem.id;
      const reason = content.value;
      insertZhjkAlarmReport({
        alarmId: cid,
        reportBy: "system_admin",
        reportById: "c485a92a74fed468dcec144238d6c55c",
        reportTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        reason: reason,
      }).then((res) => {
        res.code == "00000"
          ? ElMessage.success({
              message: "上报意见提交成功",
            })
          : ElMessage.error({
              message: res.message,
            });
        content.value = "";
        visible.value = false;
      });
    },
  },
];
const paginationConfig = ref({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 15, 30, 50],
  total: 80,
  disabled: false,
});
const unread = () => {
  visible.value = false;
  show.value = true;
};
const videoUrl = ref("");
const handlePlayerClick = (flv) => {
  showPlayer.value = true;
  videoUrl.value = flv;
};
// const getTotal = async () => {
//   const res = await request("/admin/pipe/monitor/alarm/queryIsReadAlarmCount");
//   paginationConfig.value.total = res.data ?? 0;
// };
// onMounted(getTotal);
watch(
  () => prop.count,
  () => {
    paginationConfig.value.total = prop.count;
  },
  {
    immediate: true,
  }
);
const currentChange = (page) => {
  paginationConfig.value.currentPage = page;
};
const sizeChange = (size) => {
  paginationConfig.value.pageSize = size;
};
const confirm = async () => {
  const res = await updateIsReadAlarm({
    id: compRefData.value.currentWarningItem.id,
    isRead: 0
  });
  res.code == "00000"
    ? ElMessage.success({
        message: "操作成功",
      })
    : ElMessage.error({
        message: res.message,
      });
  if (res.code == "00000") {
    compRefData.value.currentWarningItem.detail.isRead = 0;
    const obj = apisMap.get("getInspectionData");
    const res =await obj.api_pro({
      size: paginationConfig.value.pageSize,
      current: paginationConfig.value.currentPage,
    });    
    obj.callback(res);
  }
  show.value = false;
};
const isChecked = ref(false);
watch(isChecked, () => {
  localStorage.setItem("isChecked", isChecked.value);
});
watch(
  () => [paginationConfig.value.currentPage, paginationConfig.value.pageSize],
  async (val, oldVal) => {
    const fn = apisMap.get("getInspectionData");
    const res = await fn.api_pro({
      size: paginationConfig.value.pageSize,
      // current: val[1] == oldVal[1] ? paginationConfig.value.currentPage : 1,
      current: paginationConfig.value.currentPage,
    });
    fn.callback(res);
  }
);
const curDetailInfo = computed(() => {
  const currentWarningItem = compRefData.value.currentWarningItem;
  if (!currentWarningItem) return null;
  const {
    equipmentName,
    deviceId,
    cabin,
    partitionArea,
    alarmDescription,
    alarmLevel,
    alarmClassify,
    handleStatus,
    source,
    handleType,
    isRead,
    imgUrl,
  } = currentWarningItem?.detail || {};  
  const deviceName = (equipmentName + "").split("#").at(-1);
  const handleTypeText = {
    0: "未处理",
    1: "处理中",
    2: "已处理",
  }[(handleStatus + "").trim()];
  const resultText = {
    0: "误报处理",
    1: "上报处理",
    2: "工单处理",
  }[(handleStatus + "").trim()];
  return {
    content: `设备${deviceName || "_"}发生故障(设备编号:${deviceId || "_"})在${
      cabin || "告警区域"
    }发生${alarmDescription || "_"},请及时处理!`,
    title: `${cabin || "告警区域"}发生${
      alarmLevel !== null && alarmLevel !== undefined
        ? handleWaning(alarmLevel).label + "级告警"
        : "告警"
    }${isRead == 0 ? "[未读]" : "[已读]"}`,
    alarmLevel,
    alarmClassify,
    resultText,
    source: source == 1 ? "本系统告警" : "ai告警",
    handleStatus: handleTypeText,
    imgUrl,
  };
});
const handleClose = () => {
  console.log("close");
  compRefData.value.showCloseConfirm = true;
};
const pageRefData = ref({
  section2: {
    total: 10,
  },
  tip: {
    visible: true,
    titles: markRaw([
      {
        label: "序号",
        prop: "k1",
        dir: "center",
      },
      {
        label: "分区名称",
        prop: "k2",
      },
      {
        label: "舱室名称",
        prop: "k3",
        maxLength: 6,

        tooltip: true,
      },
      {
        label: "设备名称",
        prop: "k4",
        maxLength: 6,

        tooltip: true,
      },
      {
        label: "告警等级",
        prop: "k5",
        maxLength: 6,

        tooltip: true,
      },
      {
        label: "设备描述",
        prop: "k6",
        maxLength: 6,

        tooltip: true,
      },
      {
        label: "告警类型",
        prop: "k7",
        maxLength: 6,
        tooltip: true,
      },
      {
        label: "已读状态",
        prop: "k8",
        maxLength: 6,
        tooltip: true,
      },
      {
        label: "告警时间",
        prop: "k9",
        tooltip: true,
        maxLength: 6,
      },
      {
        label: "查看",
        prop: "k10",
      },
    ]),
    condition: {
      keyWord: "",
    },
    tableData: [],
    t4TableClickHandle(payload) {
      const { row = {} } = payload;
      const params = {
        type: "web_device_status__change",
        data: {
          text: row.k2,
        },
      };
      sendToUE5(params);
    },
  },
});
const opts = {
  handleCloseWarning() {
    // 关闭报警通知，才代表已读
    compRefData.value.showCloseConfirm = true;
  },
  confirmOptHandle(type) {
    if (type === "cancel") {
      return (compRefData.value.showCloseConfirm = false);
    }
    compRefData.value.showAroundWarning = false;
    META.value.show_warning = false;
  },
  handleWarningItemClick(warningItem) {
    compRefData.value.currentWarningItem = markRaw(warningItem);
    console.log("handleWarningItemClick", warningItem);
    compRefData.value.showTip = true;
  },
  handleDetailClose() {
    const currentWarningItem = compRefData.value.currentWarningItem;
    if (currentWarningItem) {
      updateIsReadAlarm({ id: currentWarningItem.id }).then((res) => {
        const warningData = compRefData.value.warningData;
        compRefData.value.warningData = markRaw(
          warningData.filter((item) => item.id !== currentWarningItem.id)
        );
        opts.setWarningCount();
      });
    }
  },

  setWarningCount() {
    META.value.warning_count = compRefData.value.warningData.length;
    console.log("META.value.warning_count", META.value.warning_count);
  },
  locationDevice() {
    const equipmentCode = compRefData.value.currentWarningItem.equipmentCode;
    // 定位报警设备位置
    console.log("locationDevice", { equipmentCode });
    const params = {
      type: "web_device_status__change",
      data: {
        text: equipmentCode,
      },
    };
    sendToUE5(params);
    pageRefData.value.tip.visible = false;
    compRefData.value.showTip = false;
    compRefData.value.showCloseConfirm = false;
    compRefData.value.showAroundWarning = false;
    META.value.show_warning = false;
  },
};

const apisMap = new Map([
  [
    "getInspectionData",
    {
      api_pro: getInspectionData,
      params: {
        size: 10,
        current: paginationConfig.value.currentPage,
      },
      callback(handleData) {
        compRefData.value.warningData = markRaw(handleData);
        pageRefData.value.tip.tableData = handleData.map((item, index) => {
          const { cabin, partitionArea, equipmentName, alarmLevel, isRead } =
            item.detail;
          const { warningTime } = item;
          return {
            k1:
              (paginationConfig.value.currentPage - 1) *
                paginationConfig.value.pageSize +
              index +
              1,
            k2: partitionArea,
            k3: cabin,
            k4: equipmentName,
            k5: alarmLevel,
            k6: item.alarmDescription,
            k7: item.type,
            k8: isRead == 0 ? "未读" : "已读",
            k9: warningTime,
            k10: "查看",
            item,
          };
        });
        console.log(
          pageRefData.value.tip.tableData,
          "pageRefData.value.tip.tableData111"
        );

        opts.setWarningCount();
      },
    },
  ],
]);
const flvs = ref([]);
const onItemClick = async (item) => {  
  const { row } = item;
    await updateIsReadAlarm({
    id: row.item.id,
    isRead: 1
  });
  const { cabinCode } = row?.item?.detail;
  compRefData.value.showTip = true;
  compRefData.value.currentWarningItem = row?.item;
    compRefData.value.currentWarningItem.detail.isRead = 1
  let devices = getJSONFromStorage("devices");
  if (devices.length > 0) {
    const device = devices.find(
      (i) => i == compRefData.value.currentWarningItem.equipmentCode
    );
    if (device) {
      compRefData.value.currentWarningItem.status = "yes";
    } else {
      compRefData.value.currentWarningItem.status = "no";
    }
  }
  console.log(
    compRefData.value.currentWarningItem,
    "compRefData.value.currentWarningItem"
  );

  const res = await getVideoMonitorData({
    cabinCode,
  });
  const ids = res.data.map((i) => i.id);
  const socket = new WebSocket(
    `ws://************:9001/websocket/${window["_apiToken"]}?api-token=${window["_apiToken"]}`
  );
  socket.onmessage = (e) => {};
  socket.onopen = () => {
    setInterval(() => {
      socket.send(
        JSON.stringify({
          deviceIds: ids,
          command: "video_live",
        })
      );
    }, 1000);
  };
  const results = await Promise.all(
    ids.map((i) =>
      getVideoLive({
        id: i,
      })
    )
  );
  flvs.value = results.map((i) => {
    const str = i.data.split("/").pop().split(".")[0];
    const item = res.data.find((item) => item.id == str);
    return {
      flv: i.data,
      item,
    };
  });
};
const getData = () => {
  // compRefData.value.currentWarningItem = null;
  patchReq([...apisMap.values()]); // 批量请求数据
};

onMounted(() => {
  const raw = localStorage.getItem("isChecked");
  if (raw) {
    isChecked.value = JSON.parse(raw);
  } else {
    isChecked.value = false;
  }
  getData();
});
</script>

<style lang="less" scoped>
.warning {
}

.opacity-liner-ani {
  animation: opc 3s linear reverse infinite;
}

@keyframes opc {
  50% {
    opacity: 0;
  }
}
:deep(.cs-t1) {
  padding: 0;
  background-color: initial;
  .e-close {
    top: 80px;
  }
}

.text-linear {
  background-image: -webkit-linear-gradient(bottom, #ffffff, #fa787d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
:deep(.ct4 .c-table) {
  .c-table-head {
    color: rgb(210, 191, 21);
    font-size: 40px;
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    pointer-events: auto;
    height: 100px;
    font-size: 35px;
    &:hover {
      cursor: pointer;
    }
    background-image: url("@/assets/imgs/comp/p59.png");
    background-size: cover;
  }
}
.el-checkbox * {
  transform: scale(2.5);
}
:deep(.el-checkbox__label) {
  padding-left: 55px !important;
}
</style>
