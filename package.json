{"name": "vite-build-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.22", "@types/three": "^0.149.0", "@vueuse/core": "^10.9.0", "animate.css": "^4.1.1", "animejs": "^3.2.2", "autofit.js": "^3.0.0", "autoprefixer": "^10.4.16", "axios": "^1.2.1", "blueimp-md5": "^2.19.0", "caniuse-lite": "^1.0.30001653", "dayjs": "^1.11.11", "echarts": "^5.4.1", "element-plus": "^2.2.27", "flv.js": "^1.6.2", "less": "^4.1.2", "less-loader": "^10.2.0", "mitt": "^3.0.1", "postcss": "^8.4.31", "style-resources-loader": "^1.5.0", "tailwindcss": "^3.3.5", "vue": "^3.5.14", "vue-router": "^4.1.6", "vuex": "^4.0.2", "xgplayer": "^3.0.19", "xgplayer-flv": "^3.0.22", "xgplayer-hls": "^3.0.19"}, "devDependencies": {"@heroicons/vue": "^2.1.4", "@types/node": "^18.11.15", "@vitejs/plugin-vue": "^4.0.0", "tailwindcss": "^3.3.5", "typescript": "^4.9.3", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^4.0.0", "vite-plugin-remove-console": "^2.2.0", "vue-tsc": "^1.0.11"}}