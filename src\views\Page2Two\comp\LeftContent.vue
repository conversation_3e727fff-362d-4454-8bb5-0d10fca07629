<template>
  <div class="text-[40px]">
    <PanelTitle
      v-loading="loading1"
      title="消防系统监测"
      :element-loading-text="msg"
    >
      <div
        class="h-[460px] px-[56px] pt-[66px] flex justify-around items-center"
      >
        <div
          v-for="item in pageRefData.section1"
          :key="item.label"
          class="flex items-end"
        >
          <div class="flex flex-col items-center">
            <div
              class="w-[232px] h-[146px] cus-bg-full bg-[url('@/assets/imgs/page2/p23.png')] relative cus-flex-center"
            >
              <div
                class="w-[90px] h-[82px] cus-bg-full absolute top-[10px]"
                :class="[item.icon_img]"
              ></div>
            </div>
            <div
              class="w-[236px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page2/p53.png')] cus-flex-center font-medium text-[#CBDDF2] mt-[20px]"
            >
              {{ item.label }}
            </div>
          </div>
          <div
            class="h-[263px] w-[254px] ml-[24px] flex flex-col items-center justify-around cus-bg-full bg-[url('@/assets/imgs/page2/p43.png')]"
          >
            <div
              v-for="detail in item.details"
              :key="detail.label"
              class="flex items-center cus-use-click"
              @click="pageHandlers.h3(item, detail)"
            >
              <span
                class="w-[10px] h-[10px] inline-block border-2 border-[#2EDFB0] rounded-[50%]"
                :style="{
                  borderColor: detail.icon_color,
                }"
              ></span>
              <span class="ml-[20px] text-[#CBDDF2]">{{ detail.label }}</span>
              <span
                class="ml-[60px] font-medium"
                :style="{
                  color: detail.label == '故障' ? detail.icon_color : '#CBDDF2',
                }"
                >{{ detail.val }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <Refresh @click="getData('l1')" class="right-[100px] top-[30px]" />
    </PanelTitle>
    <PanelTitle
      v-loading="loading2"
      title="环境参数监测"
      :element-loading-text="msg"
      class="mt-[38px]"
    >
      <div class="h-[858px] pt-[140px] px-[56px] pb-[56px]">
        <div
          class="overflow-hidden h-full"
          @mouseenter.stop="controlScroll('paused')"
          @mouseleave.stop="controlScroll('running')"
        >
          <div class="scroll-up" ref="animationElRef">
            <div class="flex flex-col gap-y-[28px] text-[#CBDDF2]">
              <div
                v-for="item in pageRefData.section2"
                :key="item.label"
                class="flex items-center bg-[url('@/assets/imgs/page2/p24.png')] bg-full w-full h-[110px] odd:bg-[url('@/assets/imgs/page2/p51.png')] even:bg-[url('@/assets/imgs/page2/p52.png')] hover:cursor-pointer"
              >
                <span class="ml-[144px] text-[#CBDDF2CC] tracking-[2px]">{{
                  item.label
                }}</span>
                <span class="ml-[16px] text-[#CBDDF24D]">{{ item.des }}</span>
                <span
                  class="ml-[auto] inline-block w-[36px] h-[36px] bg-[url('@/assets/imgs/page2/p10.png')] bg-full"
                ></span>
                <span
                  class="ml-[20px]"
                  @click="pageHandlers.section2ClickHandler(item, 'NORMAL')"
                  >正常</span
                >
                <span class="ml-[20px] text-[48px]">{{ item.val }}</span>
                <span
                  class="ml-[56px] inline-block w-[36px] h-[36px] bg-[url('@/assets/imgs/page2/p16.png')] bg-full"
                ></span>
                <span
                  class="ml-[20px]"
                  @click="pageHandlers.section2ClickHandler(item, 'WARN')"
                  >告警</span
                >
                <span class="ml-[20px] text-[48px]">{{ item.warn }}</span>
              </div>
            </div>
            <div class="flex flex-col gap-y-[28px] text-[#CBDDF2] mt-[28px]">
              <div
                v-for="item in pageRefData.section2"
                :key="item.label"
                class="flex items-center bg-[url('@/assets/imgs/page2/p24.png')] bg-full w-full h-[110px] odd:bg-[url('@/assets/imgs/page2/p51.png')] even:bg-[url('@/assets/imgs/page2/p52.png')] hover:cursor-pointer"
              >
                <span class="ml-[144px] text-[#CBDDF2CC]">{{
                  item.label
                }}</span>
                <span class="ml-[16px] text-[#CBDDF24D]">{{ item.des }}</span>
                <span
                  class="ml-[auto] inline-block w-[36px] h-[36px] bg-[url('@/assets/imgs/page2/p10.png')] bg-full"
                ></span>
                <span
                  @click="pageHandlers.section2ClickHandler(item, 'NORMAL')"
                  class="ml-[20px]"
                  >正常</span
                >
                <span class="ml-[20px] text-[48px]">{{ item.val }}</span>
                <span
                  class="ml-[56px] inline-block w-[36px] h-[36px] bg-[url('@/assets/imgs/page2/p16.png')] bg-full"
                ></span>
                <span
                  @click="pageHandlers.section2ClickHandler(item, 'WARN')"
                  class="ml-[20px]"
                  >告警</span
                >
                <span class="ml-[20px] text-[48px]">{{ item.warn }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Refresh @click="getData('l2')" class="right-[100px] top-[30px]" />
    </PanelTitle>
    <PanelTitle
      v-loading="loading3"
      :element-loading-text="msg"
      title="通信监测"
      class="mt-[24px]"
    >
      <div class="h-[486px] pt-[126px] px-[56px]">
        <div class="h-[198px] flex justify-evenly relative">
          <!-- 中间图标 -->
          <div class="relative w-[198px] h-[198px] cus-flex-center">
            <div
              class="absolute w-full h-full bg-[url('@/assets/imgs/page2/p30.png')] cus-bg-full animate-turn2"
            ></div>
            <div
              class="absolute w-[172px] h-[172px] bg-[url('@/assets/imgs/page2/p29.png')] cus-bg-full animate-turn"
            ></div>
            <div
              class="absolute w-[165px] h-[165px] bg-[url('@/assets/imgs/page2/p32.png')] cus-bg-full animate-turn2"
            ></div>
            <div
              class="absolute w-[156px] h-[173px] bg-[url('@/assets/imgs/page2/p25.png')] cus-bg-full animate-turn"
            ></div>
            <div
              class="absolute w-[66px] h-[46px] bg-[url('@/assets/imgs/page2/p13.png')] cus-bg-full"
            ></div>
            <div class="absolute top-[218px] text-center text-[#CBDDF2FF]">
              话机
            </div>
          </div>
          <div v-for="item in section3" :key="item.label">
            <div
              class="w-[314.6px] h-[90px] cus-flex-center text-[#CBDDF2FF] bg-[url('@/assets/imgs/page2/p54.png')] bg-full mt-[72px] cus-use-click"
              @click="pageHandlers.h4(item)"
            >
              {{ item.label }}
            </div>
            <div class="text-center mt-[42px] text-[#CBDDF2FF] font-medium">
              {{ item.val }}
            </div>
          </div>
        </div>
        <div
          class="w-full h-[60px] cus-bg-full bg-[url('@/assets/imgs/page2/p24.png')] mt-[32px]"
        ></div>
      </div>
      <Refresh @click="getCommon" class="right-[100px] top-[30px]" />
    </PanelTitle>
    <PanelTitle
      v-loading="loading4"
      title="视频监控"
      :element-loading-text="msg"
      class="mt-[44px] relative"
    >
      <div
        class="absolute right-[66px] top-[26px] w-[208px] h-[66px] bg-[url('@/assets/imgs/page1/p84.png')] cus-bg-full flex items-center"
      >
        <!-- 监控图标 -->
        <div
          class="w-[148px] h-[135px] bg-[url('@/assets/imgs/page1/p5.png')] cus-bg-full cursor-pointer"
          @click="pageRefData.pullVisible = pageRefData.pullVisible"
        ></div>
        <!-- 确认图标 -->
        <div
          class="w-[98px] h-[66px] flex cus-flex-center bg-[url('@/assets/imgs/page1/p85.png')] cus-bg-full cursor-pointer"
          @click="pageHandlers.changeSection5Handle"
        >
          <div
            class="w-[57px] h-[24px] bg-[url('@/assets/imgs/page1/p23.png')] cus-bg-full scale-110"
          ></div>
        </div>
        <div
          class="absolute top-[82px] right-0 w-[348px] h-[520px] bg-[url('@/assets/imgs/page1/p83.png')] cus-bg-full py-[20px] px-[20px] z-10"
          :style="{
            visibility: pageRefData.pullVisible ? 'visible' : 'hidden',
          }"
        >
          <div v-for="item in pageRefData.section5" :key="item.title">
            <h3 class="text-[32px] text-[#CBDDF2FF]">{{ item.title }}</h3>
            <div class="flex flex-col pl-[52px] py-[12px] gap-y-[20px]">
              <div
                v-for="child in item.children"
                :key="child.label"
                class="cursor-pointer"
                @click="child.open = !child.open"
              >
                <span
                  type="checkbox"
                  class="inline-block w-[32px] h-[32px] cus-bg-full"
                  :class="[child.open ? 'bg-check2' : 'bg-check1']"
                ></span>
                <span class="ml-[16px]">{{ child.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="h-[810px] pt-[202px] px-[56px]">
        <div
          class="basis-1/4 flex flex-wrap h-[460px] pt-[10px] gap-10 pl-[40px] overflow-scroll"
        >
          <div
            v-for="i in flvs"
            class="w-[30%] h-[400px] cus-bg-full cus-use-click"
            @click="pageHandlers.section4ClickHandler(i)"
          >
            <kt-xgplayer
              :url="i.flv"
              :autoplay="false"
              class="pointer-events-none"
            >
              <div class="text-[20px] absolute bottom-[40px] right-[20px]">
                {{ i.item.objectName }} {{ i.item.name }}
              </div>
            </kt-xgplayer>
          </div>
        </div>
        <div
          class="w-[1660px] h-[208px] cus-bg-full bg-[url('@/assets/imgs/page1/p10.png')] mx-auto -mt-[132px] relative -z-10"
        ></div>
        <div class="flex justify-center gap-[96px] mt-[50px]">
          <div
            class="pre w-[62px] h-[48px] cus-bg-full bg-[url('@/assets/imgs/page1/p52.png')]"
          ></div>
          <div
            class="next w-[62px] h-[48px] cus-bg-full bg-[url('@/assets/imgs/page1/p53.png')]"
          ></div>
        </div>
      </div>
      <Refresh @click="getVideo(true)" class="right-[300px] top-[25px]" />
    </PanelTitle>

    <!-- 漫游模式切换 -->
    <div
      class="absolute top-[342px] left-[2280px] flex flex-col gap-y-[24px]"
      v-show="META.corridor_side === '廊内'"
    >
      <div
        v-for="(item, index) in flyConfig.labels"
        :key="item.type"
        class="cus-use-click w-[92px] h-[92px] cus-bg-full cus-flex-center relative c-p"
        :class="[
          pageRefData.roaming_mode === item.type ? 'page2-p116' : 'page2-p114',
        ]"
        @click="flyConfig.callback(item)"
      >
        <div
          class="w-[60px] h-[60px] cus-bg-full absolute bg-[url('@/assets/imgs/page2/p112.png')]"
          :class="[
            pageRefData.roaming_mode === item.type ? 'animate-turn3' : '',
          ]"
        ></div>
        <div
          class="w-[36px] h-[36px] cus-bg-full absolute bg-[url('@/assets/imgs/page2/p112.png')]"
          :class="[item.icon_class]"
        ></div>
        <div
          class="c-tip absolute z-30 -translate-y-[100px] left-[30px] text-white w-[262px] h-[112px] cus-bg-full cus-flex-center bg-[url('@/assets/imgs/page2/p122.png')] pointer-events-none"
        >
          <span class="absolute top-[20px]">{{ item.label }}</span>
        </div>
      </div>
      <div
        class="px-[20px] py-[16px] tacking-[4px] cus-use-click bg-[#3498db] shrink-0 text-nowrap rounded-lg hover:bg-[#2980b9] whitespace-nowrap absolute left-[130px]"
        @click="pageStaticData.autoStatusChangeHandle"
        v-show="pageRefData.roaming_mode === 'auto'"
      >
        <span>{{ pageRefData.auto_status === "play" ? "暂停" : "播放" }}</span>
      </div>
    </div>

    <!-- 环境参数监测点击弹窗 -->
    <My-Tip
      stl="left-[1994px] top-[904px]"
      v-model:visible="pageRefData.tips.tip1.visible"
    >
      <!-- <div>设备详情</div> -->
      <div class="flex flex-col gap-y-[20px]">
        <div
          v-for="item in pageRefData.tips.tip1.detail"
          :key="item.label"
          class="text-[36px]"
        >
          <span>{{ item.label }}</span> : <span>{{ item.value }}</span>
        </div>
      </div>
    </My-Tip>

    <!-- 视频大弹窗 -->
    <Teleport to="#app-main">
      <My-Tip
        stl="p1-ct-2"
        v-model:visible="pageRefData.tips.tip2.visible"
        center
        close
        show-mask
      >
        <!-- <div
          class="w-[3273px] h-[1885px] cus-bg-full bg-[url('@/assets/imgs/page1/p118.png')] pt-[54px] px-[40px]"
        >
          <div
            class="w-[302px] h-[56px] cus-bg-full bg-[url('@/assets/imgs/page1/p119.png')] ml-[54px]"
          ></div>
          <div
            class="w-[3118px] h-[1632px] cus-bg-full bg-[url('@/assets/imgs/page1/p98.png')] mt-[64px] ml-[54px]"
          >
            <kt-xgplayer :url="url" class="w-[3118px] h-[1632px]" />
          </div>
        </div> -->
         <div
          class="w-[2076px] h-[1195px] cus-bg-full bg-[url('@/assets/imgs/page1/p118.png')] pt-[54px] px-[40px]"
        >
          <div
            class="w-[251.6px] h-[46.6px] cus-bg-full bg-[url('@/assets/imgs/page1/p119.png')] ml-[34px]"
          ></div>
          <div
            class="w-[1977px] h-[1035px] cus-bg-full mt-[54px] kt-flex pl-[20px] text-[100px]"
          >
            <kt-xgplayer :url="url" class="w-[1977px] h-[1000px]" />
          </div>
        </div>
      </My-Tip>
    </Teleport>
    <My-Tip
      stl="cs-t1 top-[904px]"
      v-model:visible="pageRefData.tip.visible"
      show-mask
      close
      center
      :auto-close="false"
      @close-tip="closeTip"
    >
      <div
        class="w-[2548px] h-[1738px] cus-bg-full bg-[url('@/assets/imgs/page1/p115.png')] ccc"
      >
        <div class="font-medium pt-[20px] pl-[72px]">
          <span
            class="text-linear text-[76px] font-bold family-Regular tracking-[4px] italic"
            >设备列表</span
          >
        </div>
        <!-- <div class="flex items-center mt-[40px] relative z-10">
          <div class="w-[460px] flex items-center ml-[100px]">
            <Listbox
              class="scale-[2] ml-auto mr-[160px]"
              :options="pageRefData.tip.condition.options"
              v-model="pageRefData.tip.condition.byField"
              @change="pageRefData.tip.changeByField"
            />
          </div>
          <div
            class="relative flex items-center"
            v-resetFocus
            v-show="pageRefData.tip.condition.byField == '设备名称'"
          >
            <input
              type="text"
              placeholder="请输入关键字"
              v-model="pageRefData.tip.condition.keyWord"
              class="w-[980px] h-[92px] px-4 text-[40px] transition-all border-2 rounded outline-none border-slate-200 focus:border-emerald-500 bg-[#78C4EE88] text-[#dee7f1] placeholder-[#dee7f1]"
            />
            <div
              class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] absolute right-[12px] cus-use-click"
              @click="pageRefData.tip.searchHandle('search')"
            ></div>
          </div>
          <div
            v-show="pageRefData.tip.condition.byField == '设备位置'"
            class="h-[92px] flex items-center"
          >
            <el-cascader
              :options="options"
              v-model="pageRefData.tip.condition.localIds"
              clearable
              :props="{
                checkStrictly: true,
              }"
              style="width: 240px"
              class="scale-[3] ml-[220px]"
            />
            <div class="p-[12px] border-2 ml-[280px] rounded-md">
              <div
                class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] cus-use-click cus-flex-center"
                @click="pageRefData.tip.searchHandle"
              ></div>
            </div>
          </div>
          <div
            class="h-[92px] flex items-center"
            v-show="pageRefData.tip.condition.byField == '设备类型'"
          >
            <el-select
              v-model="pageRefData.tip.condition.deviceTypeIds"
              multiple
              collapse-tags
              placeholder="选择设备类型"
              style="width: 240px"
              class="scale-[2.9] ml-[210px]"
            >
              <el-option
                v-for="item in pageRefData.tip.condition.typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
            <div class="p-[12px] border-2 ml-[280px] rounded-md">
              <div
                class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] cus-use-click cus-flex-center"
                @click="pageRefData.tip.searchHandle"
              ></div>
            </div>
          </div>
        </div> -->
        <div class="ct4 w-[full] h-[1248px] mt-[40px] pl-[64px] pr-[52px]">
          <C-Table
            :titles="pageRefData.tip.titles"
            :table-data="pageRefData.tip.tableData"
            @item-click="pageRefData.tip.t4TableClickHandle"
            max-height="1200px"
            gap="28px"
          >
           <template #k6="{ row }">
              <el-tooltip
                v-if="row.status == 'no'"
                content="当前设备不能定位"
                placement="top"
                effect="dark"
                teleported
              >
                <div
                  :style="{ cursor: 'not-allowed', opacity: 0.5 }"
                  class="w-[52px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p117.png')]"
                ></div>
              </el-tooltip>
              <div
                v-else
                :style="{ cursor: 'pointer' }"
                class="w-[52px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p117.png')]"
              ></div>
            </template>
          </C-Table>
        </div>
        <div class="flex justify-center">
          <el-pagination
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            :page-sizes="paginationConfig.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @size-change="pageRefData.tip.searchHandle"
            @current-change="pageRefData.tip.searchHandle"
            class="scale-[2.6] mt-[120px]"
            locale="zh-cn"
          />
        </div>
      </div>
    </My-Tip>
    <!-- 消防系统检测点击弹窗 -->
    <My-Tip
      stl="left-[1994px] top-[604px]"
      v-model:visible="pageRefData.tips.tip3.visible"
    >
      <!-- <div>设备详情</div> -->
      <div class="flex flex-col gap-y-[20px]">
        <div
          v-for="item in pageRefData.tips.tip3.detail"
          :key="item.label"
          class="text-[36px]"
        >
          <span>{{ item.label }}</span> : <span>{{ item.value }}</span>
        </div>
      </div>
    </My-Tip>

    <!-- 通信监测点击弹窗 -->
    <My-Tip
      stl="left-[1994px] top-[1904px]"
      v-model:visible="pageRefData.tips.tip4.visible"
    >
      <!-- <div>设备详情</div> -->
      <div class="flex flex-col gap-y-[20px]">
        <div
          v-for="item in pageRefData.tips.tip4.detail"
          :key="item.label"
          class="text-[36px]"
        >
          <span>{{ item.label }}{{ cabinCode }}</span> :
          <span>{{ item.value }}</span>
        </div>
      </div>
    </My-Tip>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  markRaw,
  onBeforeMount,
  computed,
  watch,
  onBeforeUnmount,
  onUnmounted,
} from "vue";
import { getImg, getMp4Url } from "@/utils/assets";
import pollingManager from '@/utils/pollingManager'
import PanelTitle from "@/components/my-ui/PanelTitle.vue";
import { getInspectionData, getCommunicationStatus } from "@/axios/apis/page2";
import patchReq from "@/utils/patchReq";
import META, { watchEvents, cancelEvents } from "@/META";
import Refresh from "@/components/my-ui/Refresh.vue";
import { getJSONFromStorage, processedOptions } from "@/utils/index";
import {
  getVideoMonitorData,
  getVideoLive,
  getDevicesData,
  getDeviceData2,
  getDeviceTypeLabelMap,
  getTreeSelectData,
} from "@/axios/apis/page1";
import { useStore } from "vuex";
const videoList = ref([]);
const flvs = ref([]);
const props = defineProps({
  // props
});
const url = ref("");
const h3Id = ref("");
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const loading4 = ref(false);
const store = useStore();
const mapCode = {
  综合舱: "zhc",
  电力舱: "dlc",
};
const msg = ref("");
const currentBigVideo = computed(() => {
  return META.value.cabin === "综合舱" ? getMp4Url("p-v1") : getMp4Url("p-v2");
});
const options = computed(() =>
  processedOptions(pageRefData.value.tip.condition.treeSelectOption)
);
const statusobj = {
  0: "NORMAL",
  1: "WARN",
  2: "FAULT",
};
const animationElRef = ref(null);
// 控制动画滚动
const controlScroll = (type) => {
  const animationEl = animationElRef.value;
  animationEl.style.animationPlayState = type;
};
const getVideo = async (flag) => {
  try {
    flag && (loading4.value = true);
    const res = await getVideoMonitorData({
      cabinCode: "tonghenanlu_fenqu" + store.state.pageTwoCode[1],
      //+
      // "_" +
      // store.state.pageTwoCode[0],
    });
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
    videoList.value = res.data;
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    loading4.value = false;
  }
};
let task = null
onMounted(() => {
  getVideo();
  getCommon();
  task = () =>  ["l1", "l2", "l3"].forEach((i) => getData(i));
  // pollingManager.register(getCommon)
  pollingManager.register(task)
});
onUnmounted(() => {
  // pollingManager.unregister(getCommon)
  pollingManager.unregister(task)
})
const obj = {
  idle: "空闲",
  offline: "离线",
  busy: "在线",
};
watch(
  () => store.state.pageTwoCode,
  () => {
    ["l1", "l2", "l3"].forEach((i) => getData(i));
  }
);
const section3 = ref();
const getCommon = async () => {
  let arr;
  try {
    loading3.value = true;
    const [code, num] = store.state.pageTwoCode;
    const res = await getCommunicationStatus({
      cabinCode: "tonghenanlu_fenqu" + num + "_" + code,
    });
    arr = Object.entries(res.data || {}).map((item) => {
      return {
        label: obj[item[0]],
        val: item[1],
      };
    });
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    loading3.value = false;
  }
  section3.value = arr;
};
const socket = ref(null);
watch(
  videoList,
  async (val) => {
    const ids = videoList.value.map((i) => i.id);
    const promises = videoList.value.map((item) => {
      return getVideoLive({
        id: item.id,
      });
    });
    const flvRes = await Promise.all(promises);
    flvs.value = flvRes.map((i) => {
      const str = i.data.split("/").pop().split(".")[0];
      const item = videoList.value.find((item) => item.id == str);
      return {
        flv: i.data,
        item,
      };
    });
    socket.value = new WebSocket(
      `ws://172.22.60.11:9001/websocket/${window["_apiToken"]}?api-token=${window["_apiToken"]}`
    );
    socket.value.onerror = (err) => {
      console.log(err);
    };
    socket.value.onmessage = (e) => {};
    socket.value.onopen = () => {
      setInterval(() => {
        socket.value.send(
          JSON.stringify({
            deviceIds: ids,
            command: "video_live",
          })
        );
      }, 1000);
    };
  },
  {
    deep: true,
  }
);
const setType = (tableData) => {
const options = getJSONFromStorage("options");
  const devices = getJSONFromStorage("devices");
  tableData.forEach((item) => {    
    if (devices.length > 0) {
      const device = devices.find((i) => i == item.k2);
      if (device) {
        item.status = "yes";
      } else {
        item.status = "no";
      }
    }
    const info = options.find((i) => i.id == item.deviceTypeId);
    if (info) item.k3 = info.label;
  });
};
const closeTip = () => {
  curId.value = "";
  h3Id.value = "";
};
const pageStaticData = {
  autoStatusChangeHandle() {
    const pageRefDataValue = pageRefData.value;
    pageRefDataValue.auto_status =
      pageRefDataValue.auto_status === "play" ? "pause" : "play";

    const params = {
      type: "web_auto_status__change",
      data: {
        text: pageRefDataValue.auto_status,
      },
    };
    sendToUE5(params);
  },
  section3: [
    {
      label: "在线",
      val: "0",
      prop: "busy",
    },
    {
      label: "离线",
      val: "0",
      prop: "offline",
    },
    {
      label: "通话",
      val: "0",
    },
    {
      label: "空闲",
      val: "0",
      prop: "idle",
    },
  ],
};
const queryStatus = ref("");
const pageRefData = ref({
  pullVisible: false,
  roaming_mode: "manual",
  auto_status: "play",
  tip: {
    visible: false,
    paginationConfig: {
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 15, 30, 50],
      total: 80,
      disabled: false,
    },
    condition: {
      byField: "设备名称",
      keyWord: "",
      deviceTypeIds: [],
      localIds: [],
      options: [
        {
          value: "设备名称",
          label: "设备名称",
        },
        {
          value: "设备位置",
          label: "设备位置",
        },
        {
          value: "设备类型",
          label: "设备类型",
        },
      ],
      treeSelectOption: [],
      typeOptions: [],
    },
    titles: markRaw([
      {
        label: "序号",
        prop: "k1",
        dir: "center",
      },
      {
        label: "设备编号",
        prop: "k2",
      },
      {
        label: "设备类型",
        prop: "k3",
      },
      {
        label: "设备名称",
        prop: "k4",
      },
      {
        label: "状态",
        prop: "k5",
      },
      // {
      //   label: "定位",
      //   prop: "k6",
      // },
    ]),
    tableData: [],
    t4TableClickHandle(payload) {
      const { row = {} } = payload;
      const params = {
        type: "web_device_status__change",
        data: {
          text: row.k2,
        },
      };
      sendToUE5(params);
      pageRefData.value.tip.visible = false
    },
    searchHandle(type) {
      if (type == "search") {
        curId.value = "";
        h3Id.value = "";
      }
      const tip = pageRefData.value.tip;
      const apiItem = apisMap3.get("getDevicesData");
      const apiItem2 = apisMap2.get("getDeviceData2");
      if (tip.condition.byField == "设备位置") {
        const localId = (tip.condition.localIds || []).at(-1);
        const params = {
          code: localId,
          current: tip.paginationConfig.currentPage,
          page: tip.paginationConfig.pageSize,
        };

        apiItem2.api_pro(params).then(apiItem2.callback);
      } else {
        const params = {
          ...apiItem.params,
          current: tip.paginationConfig.currentPage,
          size: tip.paginationConfig.pageSize,
        };
        if (tip.condition.byField == "设备名称") {
          params.query = {};
          if (curId.value) {
            params.query.deviceTypeIds = [curId.value];
          }
          if (h3Id.value) {
            params.query.deviceTypeIds = [h3Id.value];
          }
          if (queryStatus.value) {
            Object.assign(params.query, {
              deviceStatuses: [queryStatus.value],
            });
          }
        } else if (tip.condition.byField == "设备类型") {
          params.query = {
            deviceTypeIds: tip.condition.deviceTypeIds,
          };
          if (queryStatus.value) {
            Object.assign(params, query, {
              deviceStatuses: queryStatus,
            });
          }
        }
        apiItem.api_pro(params).then(apiItem.callback);
      }
    },
    changeByField() {
      const condition = pageRefData.value.tip.condition;

      condition.keyWord = "";
      condition.deviceTypeIds.length = 0;
      condition.localIds.length = 0;
    },
  },
  section1: markRaw([
    {
      label: "烟感探头",
      icon_img: "page2-p17",
      details: [
        {
          label: "正常",
          val: "5",
          icon_color: "#2EDFB0",
        },
        {
          label: "告警",
          val: "0",
          icon_color: "#FDFF8B",
        },
        {
          label: "故障",
          val: "1",
          icon_color: "#FF7184",
        },
      ],
    },
    {
      label: "手报",
      icon_img: "page2-p11",
      details: [
        {
          label: "正常",
          val: "5",
          icon_color: "#2EDFB0",
        },
        {
          label: "告警",
          val: "0",
          icon_color: "#FDFF8B",
        },
        {
          label: "故障",
          val: "1",
          icon_color: "#FF7184",
        },
      ],
    },
    {
      label: "防火门",
      icon_img: "page2-p11.webp",
      details: [
        {
          label: "正常",
          val: "5",
          icon_color: "#2EDFB0",
        },
        {
          label: "告警",
          val: "0",
          icon_color: "#FDFF8B",
        },
        {
          label: "故障",
          val: "1",
          icon_color: "#FF7184",
        },
      ],
    },
  ]),
  section2: markRaw([
    {
      label: "湿温度监测",
      val: "420",
      warn: "320",
      des: "Wet temperature monitoring",
    },
    {
      label: "氧含量监测",
      val: "321",
      warn: "521",
      des: "Oxygen content monitoring",
    },
    {
      label: "甲烷含量监测",
      val: "421",
      warn: "123",
      des: "Methane content monitoring",
    },
    {
      label: "硫化氧监测",
      val: "420",
      warn: "322",
      des: "Sulfide oxygen monitoring",
    },
    {
      label: "液位监测",
      val: "231",
      warn: "420",
      des: "Liquid level monitoring",
    },
  ]),
  section5: [
    {
      title: "监控中心",
      children: [
        {
          label: "监控1",
          open: true,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
    {
      title: "廊内",
      children: [
        {
          label: "监控1",
          open: false,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
    {
      title: "廊外",
      children: [
        {
          label: "监控1",
          open: false,
        },
        {
          label: "监控2",
          open: true,
        },
      ],
    },
  ],
  tips: {
    tip1: {
      visible: false,
      detail: [],
    },
    tip2: {
      visible: false,
      detail: [],
    },
    tip3: {
      visible: false,
      detail: [],
    },
    tip4: {
      visible: false,
      detail: [],
    },
  },
});
watch(
  () => pageRefData.value.tip.visible,
  (val) => {
    if (!val) {
      console.log(pageRefData.value);

      Object.assign(pageRefData.value.tip.condition, {
        keyWord: "",
        deviceTypeIds: [],
        localIds: [],
      });
    }
  }
);
const paginationConfig = pageRefData.value.tip.paginationConfig;
const apisMap2 = new Map([
  [
    "getDeviceData2",
    {
      api_pro: getDeviceData2,
      params: {
        objectId:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
      callback(handledData) {
        const { tableData, pageInfo } = handledData;
        pageRefData.value.tip.tableData = markRaw(tableData);
        Object.assign(pageRefData.value.tip.paginationConfig, {
          currentPage: pageInfo.current,
          pages: pageInfo.pages,
          pageSize: pageInfo.size,
          total: pageInfo.total,
        });
      },
    },
  ],
]);
const apisMap = new Map([
  [
    "getTreeSelectData",
    {
      api_pro: getTreeSelectData,
      params: {},
      callback(selectData) {
        pageRefData.value.tip.condition.treeSelectOption = selectData;
      },
    },
  ],
  [
    "getDeviceTypeLabelMap",
    {
      api_pro: getDeviceTypeLabelMap,
      params: {},
      callback(typeOptions) {
        pageRefData.value.tip.condition.typeOptions = typeOptions;
      },
    },
  ],
  [
    "getInspectionData",
    {
      api_pro: getInspectionData,
      params: {
        code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
      callback(handledData) {        
        msg.value = handledData.code == "00000" ? "请求成功" : "请求失败";
        pageRefData.value.section1 = markRaw(
          handledData.fire_fighting_monitoring.slice(0,3) || []
        );
        pageRefData.value.section2 = markRaw(
          handledData.environmental_monitoring || []
        );
      },
    },
  ],
]);
const apisMap3 = new Map([
   [
    "getDevicesData",
    {
      api_pro: getDevicesData,
      params: {
        current: 1,
        size: 10,
      },
      callback(handledData) {
        const { tableData, pageInfo } = handledData;
        setType(tableData);
        pageRefData.value.tip.tableData = markRaw(tableData);
        Object.assign(pageRefData.value.tip.paginationConfig, {
          currentPage: pageInfo.current,
          pages: pageInfo.pages,
          pageSize: pageInfo.size,
          total: pageInfo.total,
        });
      },
    },
  ],
])
const curId = ref("");
const pageHandlers = {
  async section2ClickHandler(item, status) {
    const id = item.id ?? "";
    curId.value = id;
    queryStatus.value = status;
    pageRefData.value.tip.visible = true;
    const handledData = await getDevicesData({
      current: 1,
      size: 10,
      query: {
        deviceTypeIds: [id],
        deviceStatuses: [status],
         code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
        cabinCode:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
    });
    const { tableData, pageInfo } = handledData;
    setType(tableData);
    pageRefData.value.tip.tableData = markRaw(tableData);
    Object.assign(pageRefData.value.tip.paginationConfig, {
      currentPage: pageInfo.current,
      pages: pageInfo.pages,
      pageSize: pageInfo.size,
      total: pageInfo.total,
    });
    pageRefData.value.tips.tip1.detail =
      item.detail ||
      markRaw([
        {
          label: "设备编码",
          value: "test-cede-001",
        },
        {
          label: "设备名称",
          value: "防火栓",
        },
        {
          label: "运行状态",
          value: "001",
        },
        {
          label: "设备位置",
          value: "1防火分区",
        },
      ]);
  },
  section4ClickHandler(item) {
    pageRefData.value.tips.tip2.visible = true;
    // pageRefData.value.tips.tip4.detail = item.detail;
    url.value = item.flv;
  },
  async changeSection5Handle() {
    pageRefData.value.pullVisible = false;
    const [code, num] = store.state.pageTwoCode;
    const res = await getVideoMonitorData({
      cabinCode: "tonghenanlu_fenqu" + num + "_" + code,
    });
    videoList.value = res.data;
  },
  async h3(item, detail) {
    curId.value = "";
    h3Id.value = item.id;
    pageRefData.value.tip.visible = true;
    const handledData = await getDevicesData({
      current: 1,
      size: 10,
      query: {
        deviceTypeIds: [item.id],
        deviceStatuses: [statusobj[detail.status]],
         cabinCode:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
        code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
    });
    const { tableData, pageInfo } = handledData;
    setType(tableData);
    pageRefData.value.tip.tableData = markRaw(tableData);
    Object.assign(pageRefData.value.tip.paginationConfig, {
      currentPage: pageInfo.current,
      pages: pageInfo.pages,
      pageSize: pageInfo.size,
      total: pageInfo.total,
    });
    pageRefData.value.tips.tip3.detail = markRaw([
      {
        label: "设备编码",
        value: "test-xjofda302",
      },
      {
        label: "设备名称",
        value: "烟火探头",
      },
      {
        label: "运行状态",
        value: "001",
      },
      {
        label: "设备位置",
        value: "4防火分区",
      },
    ]);
  },
  async h4(item) {    
    pageRefData.value.tip.visible = true;
    curId.value = "a51a46f895a21b0a30b537a8377b5861";
    queryStatus.value = "";
    const handledData = await getDevicesData({
      current: 1,
      size: 10,
      query: {
        deviceTypeIds: ["a51a46f895a21b0a30b537a8377b5861"],
        code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
           cabinCode:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
    });
    const { tableData, pageInfo } = handledData;
    setType(tableData);
    pageRefData.value.tip.tableData = markRaw(tableData);
    Object.assign(pageRefData.value.tip.paginationConfig, {
      currentPage: pageInfo.current,
      pages: pageInfo.pages,
      pageSize: pageInfo.size,
      total: pageInfo.total,
    });
    // pageRefData.value.tips.tip4.visible = true;
    // pageRefData.value.tips.tip4.detail = markRaw([
    //   {
    //     label: "设备编码",
    //     value: "Jk0328J",
    //   },
    //   {
    //     label: "设备名称",
    //     value: "信号接收器",
    //   },
    //   {
    //     label: "运行状态",
    //     value: "002",
    //   },
    //   {
    //     label: "设备位置",
    //     value: "7分区",
    //   },
    // ]);
  },
};

const flyConfig = {
  labels: [
    {
      label: "自动漫游",
      type: "auto",
      icon_class: "page2-p115",
    },
    {
      label: "手动漫游",
      type: "manual",
      icon_class: "page2-p113",
    },
  ],
  // 漫游方式改变
  callback: (item) => {
    // if ((pageRefData.value.roaming_mode = item.type)) {
    //   pageRefData.value.roaming_mode = "";
    // } else {
    //   pageRefData.value.roaming_mode = item.type;
    // }
    pageRefData.value.roaming_mode = item.type;
    if (item.type === "auto") {
      pageRefData.value.auto_status = "play";
    }
    const params = {
      type: "fly_mode_change",
      data: {
        type: item.type,
        text: item.label,
      },
    };
    sendToUE5(params);
  },
};

const getData = async (type) => {
  const obj = {
    l1: loading1,
    l2: loading2,
    l3: loading3,
  };
  const loading = obj[type] || {};
  loading.value = true;
  await patchReq([...apisMap.values()]);
  loading.value = false;
};

const events = {
  "ue-auto-status-change": (payload) => {
    const { text } = payload;
    pageRefData.value.auto_status = text;
  },
};

onMounted(() => {
  getData();
  watchEvents(events);
});

onBeforeMount(() => {
  cancelEvents(events);
});
</script>

<style lang="less" scoped>
.p1-ct-2 {
  padding: 0;
  background-color: initial;

  .e-close {
    top: 20px;
  }
}
.page2-p17 {
  background-image: url("@/assets/imgs/page2/p17.png");
}

.page2-p11 {
  background-image: url("@/assets/imgs/page2/p11.png");
}

.page2-p12 {
  background-image: url("@/assets/imgs/page2/p12.png");
}

.page2-p113 {
  background-image: url("@/assets/imgs/page2/p113.png");
}

.page2-p114 {
  background-image: url("@/assets/imgs/page2/p114.png");
}

.page2-p115 {
  background-image: url("@/assets/imgs/page2/p115.png");
}

.page2-p116 {
  background-image: url("@/assets/imgs/page2/p116.png");
}

.p99 {
  background-image: url("@/assets/imgs/page2/p99.png");
  transform: scale(1.5);
}
.scroll-up {
  transform: translate3d(0, 0, 0);
  animation: ani-up 20s linear infinite;
}

@keyframes ani-up {
  to {
    transform: translate3d(0, -50%, 0);
  }
}

.bg-check1 {
  background-image: url("@/assets/imgs/page1/p29.png");
}

.bg-check2 {
  background-image: url("@/assets/imgs/page1/p28.png");
}

.c-p {
  .c-tip {
    display: none;
  }

  &:hover {
    .c-tip {
      display: flex;
    }
  }
}
:deep(.ct4 .c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    pointer-events: auto;
    height: 120px;

    &:hover {
      cursor: pointer;
    }

    background-color: #1581ce42;
  }
}
.ccc {
  :deep(.el-pagination) {
    color: #fff;

    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }

    .el-input__wrapper {
      background-color: #49779299;
    }

    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }

    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-select__icon {
      color: white;
    }

    .el-select__tags {
      top: 20%;

      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
.el-cascader__dropdown.el-popper {
  background-color: #353f4d;
  transform-origin: left top;
  transform: scale(0.72);

  .el-cascader-node__label {
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background: #2d506f;
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #187db0;
    background: #187db0;
  }
}

.el-popper.is-light {
  transform-origin: left top;
  transform: scale(0.72);

  background-color: #353f4d;

  .el-select-dropdown__item {
    color: #ffffffe0;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover,
  .el-select-dropdown__item.selected {
    background: #2d506f;
  }

  .el-select-dropdown__item.selected {
    color: #3ebfff;
  }
}
</style>
