<template>
  <div class="w-52">
    <Listbox v-model="selectedOption">
      <div class="relative">
        <ListboxButton
          class="relative w-full cursor-default rounded-md mb-[16px] py-3 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm bg-[#78C4EE88]"
        >
          <span
            class="block truncate text-[#ffffff] text-[20px] tracking-[2px]"
            >{{ selectedOption }}</span
          >
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <ChevronUpDownIcon class="h-5 w-5 text-white" aria-hidden="true" />
          </span>
        </ListboxButton>

        <transition
          leave-active-class="transition duration-100 ease-in"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <ListboxOptions
            class="absolute max-h-60 w-full overflow-auto rounded-md py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-30 bg-[#497792ff]"
          >
            <ListboxOption
              v-slot="{ active, selected }"
              v-for="optionItem in options"
              :key="optionItem.value"
              :value="optionItem.label"
              @click="handleOptionChange(optionItem)"
            >
              <li
                :class="[
                  active ? 'bg-[#21312e] text-amber-900' : 'text-gray-900',
                  'relative cursor-default select-none py-2 pl-10 pr-4',
                ]"
              >
                <span
                  :class="[
                    selected ? 'font-medium' : 'font-normal',
                    'block truncate text-[#ffffff]',
                  ]"
                  >{{ optionItem.label }}</span
                >
                <span
                  v-if="selected"
                  class="absolute inset-y-0 left-0 flex items-center pl-3 text-white"
                >
                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                </span>
              </li>
            </ListboxOption>
          </ListboxOptions>
        </transition>
      </div>
    </Listbox>
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from "@headlessui/vue";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid";

const emit = defineEmits(["update:modelValue"]);

const props = defineProps({
  options: {
    type: Array,
    default: () => [
      { label: "Wade Cooper", value: "wade" },
      { label: "Arlene Mccoy", value: "arlene" },
      { label: "Devon Webb", value: "devon" },
      { label: "Tom Cook", value: "tom" },
      { label: "Tanya Fox", value: "tanya" },
      { label: "Hellen Schmidt", value: "hellen" },
    ],
  },
  modelValue: {
    type: String,
    default: "",
  },
});

// const options = [
//   { label: "Wade Cooper", value: "wade" },
//   { label: "Arlene Mccoy", value: "arlene" },
//   { label: "Devon Webb", value: "devon" },
//   { label: "Tom Cook", value: "tom" },
//   { label: "Tanya Fox", value: "tanya" },
//   { label: "Hellen Schmidt", value: "hellen" },
// ];
const selectedOption = ref(props.modelValue);

const handleOptionChange = (item) => {
  emit("update:modelValue", item.value);
  emit("change", item.value);
};
</script>
