<template>
  <div
    class="w-[168px] h-[168px] cus-bg-full bg-[url('@/assets/imgs/comp/p58.png')]  cus-use-click"
    @click="changeShowWainHandle"
  >
    <div
      class="h-[64px] bg-[#F82F19FF] text-[40px] px-[20px] cus-flex-center absolute right-0 top-0 translate-y-[-50%] translate-x-[50%] rounded-full text-white"
    >
      <span>{{ warningCount }}</span>
    </div>
  </div>
</template>

<script setup>
import META from "@/META";
const props = defineProps({
  warningCount:{
    type: [Number,String],
    default: 0
  }
})
const changeShowWainHandle = () => {
  // META.value.show_warning = !META.value.show_warning;
  META.value.show_warning = true;
};
</script>

<style lang="less" scoped></style>
