<template>
  <div class="text-white h-full">
    <PageContent />
    <Left><LeftContent /></Left>
    <Right> <RightContent /></Right>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import LeftContent from "./comp/Left2.vue";
import RightContent from "./comp/RightContent.vue";
import PageContent from "./comp/PageContent.vue";
</script>

<style lang="less" scoped></style>
