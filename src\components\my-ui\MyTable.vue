<template>
  <div class="my-table table-base-style" :class="[tStyle]">
    <C-Table v-bind="$attrs">
      <template
        v-for="(slotKey, slotIndex) in Object.keys($slots)"
        :key="slotIndex"
        #[slotKey]="params"
      >
        <slot :name="slotKey" v-bind="params"></slot>
      </template>
    </C-Table>
  </div>
</template>

<script setup>
const props = defineProps({
  tStyle: {
    type: String,
    default: "style1",
  },
});
</script>

<style lang="less" scoped>
.my-table {
  &.table-base-style {
    :deep(.c-table) {
      letter-spacing: 2px;
      .c-table-head {
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        color: rgba(203, 221, 242, 1);
      }
      .c-table-row {
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        color: rgba(203, 221, 242, 1);
      }
    }
  }

  &.style1 {
    :deep(.c-table) {
      .c-table-head {
        height: 104px;
        background-image: url("@/assets/imgs/page1/p93.png");
      }
      .c-table-row {
        height: 96px;
        background-image: url("@/assets/imgs/page1/p78.png");
        position: relative;
      }
      .c-table-row::after {
        position: absolute;
        content: "";
        width: 18px;
        height: 28px;
        background-image: url("@/assets/imgs/page1/p1.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  &.style2 {
    :deep(.c-table) {
      .c-table-head {
        height: 96px;
        background-image: url("@/assets/imgs/page1/p93.png");
        margin-bottom: 24px;
        .th-cell {
          padding-left: 32px;
        }
      }
      .c-table-row {
        height: 96px;
        background-image: url("@/assets/imgs/page2/p5.png");
        .td-cell {
          position: relative;
          padding-left: 32px;
          &::before {
            position: absolute;
            content: "";
            width: 2px;
            height: 36px;
            background-color: #cbddf24d;
            left: 0;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
}
</style>
