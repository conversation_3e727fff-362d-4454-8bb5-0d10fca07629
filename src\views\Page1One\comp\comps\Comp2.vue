<template>
  <div>
    <PanelTitle
      v-loading="loading"
      :element-loading-text="msg"
      title="设备状态分析"
      class="mt-[85px] relative"
    >
      <div class="h-[456px] box-border pt-[126px] px-[56px] flex">
        <div class="flex flex-col items-center w-[214px]">
          <div
            class="w-[132px] h-[140px] cus-bg-full bg-[url('@/assets/imgs/page1/p35.png')] relative flex cus-flex-center"
          >
            <div
              class="absolute w-[52px] h-[46px] cus-bg-full bg-[url('@/assets/imgs/page1/p87.png')] top-[22px]"
            ></div>
          </div>
          <div class="text-[#CBDDF2] text-[36px] whitespace-nowrap">
            管廊设备总数
          </div>
          <div
            class="w-full h-[62px] cus-bg-full bg-[url('@/assets/imgs/page1/p58.png')] cus-flex-center flex mt-[10px]"
          >
            {{ pageRefData.section2.total }} 个
          </div>
        </div>
        <div class="flex flex-wrap ml-[64px] gap-x-[60px]">
          <div
            v-for="item in pageRefData.section2.list"
            :key="item.label"
            class="basis-2/5 grow w-[662px] h-[122px] border border-[#4DA1B2] flex items-center bg-[url('@/assets/imgs/page1/p18.png')] cus-bg-full"
          >
            <!-- 竖条背景1 -->
            <div
              class="w-[2px] h-[78px] cus-bg-full bg-[url('@/assets/imgs/page1/p19.png')] ml-[8px]"
            ></div>
            <!-- 正常 -->
            <div class="ml-[20px] min-w-[180px]">
              <div class="text-[#b4c4d6] text-[32px]">正常</div>
              <div class="text-[#CBDDF2] text-[36px] mt-[10px] font-medium">
                <span>{{ item.t1 }}</span>
                <span class="ml-[24px]">({{ item.t1p }})</span>
              </div>
            </div>
            <!-- 竖条背景2 -->
            <div
              class="w-[2px] h-[78px] cus-bg-full bg-[url('@/assets/imgs/page1/p20.png')] ml-[44px]"
            ></div>
            <!-- 告警 -->
            <div class="ml-[20px]">
              <div class="text-[#b4c4d6] text-[32px]">告警</div>
              <div class="text-[#75F8EE] text-[36px] mt-[10px] font-medium">
                <span>{{ item.t2 }}</span>
                <span class="ml-[24px]">({{ item.t2p }})</span>
              </div>
            </div>
            <div class="ml-auto">
              <div
                class="text-[32px] text-[#b4c4d6] flex items-center justify-center"
              >
                总数
                <span
                  class="text-[36px] text-[#d6e9ff] font-medium ml-[10px]"
                  >{{ item.total }}</span
                >
              </div>
              <div
                class="w-[182px] h-[16px] cus-bg-full bg-[url('@/assets/imgs/page1/p14.png')]"
              ></div>
              <div
                class="w-[182px] h-[46px] cus-bg-full bg-[url('@/assets/imgs/page1/p13.png')] text-[32px] cus-flex-center text-[#d6e9ff]"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute right-[50px] top-[26px]">
        <div
          class="rounded-[10px] cus-use-click w-[122px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p113.png')] cus-flex-center text-[36px] tracking-[4px]"
          @click="handleTip4Show"
        >
          <span>更多</span>
          <!-- <div
            class="w-[66px] h-[44px] cus-bg-full bg-[url('@/assets/imgs/page1/p114.png')]"
          ></div> -->
        </div>
      </div>
      <Refresh @click="getData(true)" class="right-[200px] top-[25px]" />
    </PanelTitle>
    <!-- 设备综合状态，设备定位 -->
    <!-- <Teleport to="#app-main"> -->
    <My-Tip
      stl="cs-t1 top-[904px]"
      v-model:visible="pageRefData.tip.visible"
      show-mask
      close
      center
      :auto-close="false"
    >
      <div
        class="w-[2548px] h-[1738px] cus-bg-full bg-[url('@/assets/imgs/page1/p115.png')] ccc"
      >
        <div class="font-medium pt-[20px] pl-[72px]">
          <span
            class="text-linear text-[76px] font-bold family-Regular tracking-[4px] italic"
            >设备列表</span
          >
        </div>
        <div class="flex items-center mt-[40px] relative z-10">
          <div class="w-[460px] flex items-center ml-[100px]">
            <Listbox
              class="scale-[2] ml-auto mr-[160px]"
              :options="pageRefData.tip.condition.options"
              v-model="pageRefData.tip.condition.byField"
              @change="pageRefData.tip.changeByField"
            />
          </div>
          <div
            class="relative flex items-center"
            v-resetFocus
            v-show="pageRefData.tip.condition.byField == '设备名称'"
          >
            <input
              type="text"
              placeholder="请输入关键字"
              v-model="pageRefData.tip.condition.keyWord"
              class="w-[980px] h-[92px] px-4 text-[40px] transition-all border-2 rounded outline-none border-slate-200 focus:border-emerald-500 bg-[#78C4EE88] text-[#dee7f1] placeholder-[#dee7f1]"
            />
            <div
              class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] absolute right-[12px] cus-use-click"
              @click="pageRefData.tip.searchHandle"
            ></div>
          </div>
          <div
            v-show="pageRefData.tip.condition.byField == '设备位置'"
            class="h-[92px] flex items-center"
          >
            <el-cascader
              :options="options"
              v-model="pageRefData.tip.condition.localIds"
              clearable
              :props="{
                checkStrictly: true,
              }"
              @change="handleChange"
              ref="cascaderRef"
              style="width: 240px"
              class="scale-[3] ml-[220px]"
            />
            <div class="p-[12px] border-2 ml-[280px] rounded-md">
              <div
                class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] cus-use-click cus-flex-center"
                @click="pageRefData.tip.searchHandle"
              ></div>
            </div>
          </div>
          <div
            class="h-[92px] flex items-center"
            v-show="pageRefData.tip.condition.byField == '设备类型'"
          >
            <el-select
              v-model="pageRefData.tip.condition.deviceTypeIds"
              multiple
              collapse-tags
              placeholder="选择设备类型"
              style="width: 240px"
              class="scale-[2.9] ml-[210px]"
            >
              <el-option
                v-for="item in pageRefData.tip.condition.typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
            <div class="p-[12px] border-2 ml-[280px] rounded-md">
              <div
                class="w-[68px] h-[68px] cus-bg-full bg-[url('@/assets/imgs/page1/p116.png')] cus-use-click cus-flex-center"
                @click="pageRefData.tip.searchHandle"
              ></div>
            </div>
          </div>
        </div>
        <div class="ct4 w-[full] h-[1248px] mt-[40px] pl-[64px] pr-[52px]">
          <C-Table
            :titles="pageRefData.tip.titles"
            :table-data="pageRefData.tip.tableData"
            @item-click="pageRefData.tip.t4TableClickHandle"
            max-height="1200px"
            gap="28px"
            @cellClick="handleLocation"
          >
            <template #k6="{ row }">
              <el-tooltip
                v-if="row.status == 'no'"
                content="当前设备不能定位"
                placement="top"
                effect="dark"
                teleported
              >
                <div
                  :style="{ cursor: 'not-allowed', opacity: 0.5 }"
                  class="w-[52px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p117.png')]"
                ></div>
              </el-tooltip>
              <div
                v-else
                :style="{ cursor: 'pointer' }"
                class="w-[52px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p117.png')]"
              ></div>
            </template>
          </C-Table>
        </div>
        <div class="flex justify-center">
          <el-pagination
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            :page-sizes="paginationConfig.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @size-change="pageRefData.tip.searchHandle"
            @current-change="pageRefData.tip.searchHandle"
            class="scale-[2.6] mt-[60px]"
            locale="zh-cn"
          />
        </div>
      </div>
    </My-Tip>
    <!-- </Teleport> -->
  </div>
</template>

<script setup>
import { ref, markRaw, onMounted, computed, onUnmounted } from "vue";
import Refresh from "@/components/my-ui/Refresh.vue";
import { getJSONFromStorage, processedOptions } from "@/utils/index";
import pollingManager from '@/utils/pollingManager'

import {
  getDeviceStatus,
  getTreeSelectData,
  getDeviceTypeLabelMap,
  getDeviceData2,
} from "@/axios/apis/page1";
import { getDevicesData } from "@/axios/apis/index";
import patchReq from "@/utils/patchReq";
const loading = ref(false);
const props = defineProps({
  flag: String,
});
const msg = ref("");
const pageRefData = ref({
  section2: {
    total: 1000,
    list: [
      {
        label: "系统1",
        t1: "100",
        t1p: "20%",
        t2: "200",
        t2p: "40%",
        total: "500",
      },
      {
        label: "系统2",
        t1: "100",
        t1p: "20%",
        t2: "200",
        t2p: "40%",
        total: "500",
      },
      {
        label: "系统3",
        t1: "100",
        t1p: "20%",
        t2: "200",
        t2p: "40%",
        total: "500",
      },
      {
        label: "系统4",
        t1: "100",
        t1p: "20%",
        t2: "200",
        t2p: "40%",
        total: "500",
      },
    ],
  },
  tip: {
    visible: false,
    paginationConfig: {
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 15, 30, 50],
      total: 80,
      disabled: false,
    },
    condition: {
      byField: "设备名称",
      keyWord: "",
      deviceTypeIds: [],
      localIds: [],
      options: [
        {
          value: "设备名称",
          label: "设备名称",
        },
        {
          value: "设备位置",
          label: "设备位置",
        },
        {
          value: "设备类型",
          label: "设备类型",
        },
      ],
      treeSelectOption: [],
      typeOptions: [],
    },
    titles: markRaw([
      {
        label: "序号",
        prop: "k1",
        dir: "center",
      },
      {
        label: "设备编号",
        prop: "k2",
      },
      {
        label: "设备名称",
        prop: "k4",
      },
      {
        label: "设备类型",
        prop: "k3",
      },
      {
        label: "设备状态",
        prop: "k5",
      },
      // {
      //   label: "数据更新时间",
      //   prop: "k6",
      // },
      {
        label: "定位",
        prop: "k6",
      },
    ]),
    tableData: [],
    t4TableClickHandle(payload) {
      const { row = {} } = payload;
      const params = {
        type: "web_device_status__change",
        data: {
          text: row.k2,
        },
      };
      sendToUE5(params);
    },
    searchHandle() {
      const tip = pageRefData.value.tip;
      const apiItem = apisMap.get("getDevicesData");
      const apiItem2 = apisMap2.get("getDeviceData2");
      if (tip.condition.byField == "设备位置") {
        const localId = (tip.condition.localIds || []).at(-1);
        const params = {
          code: localId,
          current: tip.paginationConfig.currentPage,
          size: tip.paginationConfig.pageSize,
        };
        apiItem2.api_pro(params).then(apiItem2.callback);
      } else {
        const params = {
          ...apiItem.params,
          current: tip.paginationConfig.currentPage,
          size: tip.paginationConfig.pageSize,
        };
        if (tip.condition.byField == "设备名称") {
          params.query = {
            name: tip.condition.keyWord,
          };
        } else if (tip.condition.byField == "设备类型") {
          params.query = {
            deviceTypeIds: tip.condition.deviceTypeIds,
          };
        }
        apiItem.api_pro(params).then(apiItem.callback);
      }
    },
    changeByField() {
      const condition = pageRefData.value.tip.condition;
      condition.keyWord = "";
      condition.deviceTypeIds.length = 0;
      condition.localIds.length = 0;
    },
  },
});

const options = computed(() =>
  processedOptions(pageRefData.value.tip.condition.treeSelectOption)
);
const handleLocation = (data) => {
  if (data && data.val == "定位") {
    const { row } = data;
    const params = {
      type: "web_device_status__change",
      data: {
        text: row.k2,
      },
    };
    sendToUE5(params);
    pageRefData.value.tip.visible = false;
  }
};
const paginationConfig = pageRefData.value.tip.paginationConfig;
const setType = (tableData) => {
  const options = getJSONFromStorage("options");
  const devices = getJSONFromStorage("devices");
  tableData.forEach((item) => {
    if (devices.length > 0) {
      const device = devices.find((i) => i == item.k2);
      if (device) {
        item.status = "yes";
      } else {
        item.status = "no";
      }
    }
    const info = options.find((i) => i.id == item.deviceTypeId);
    if (info) item.k3 = info.label;
  });
};
const apisMap = new Map([
  [
    "getDeviceStatus", // 设备状态分析
    {
      api_pro: getDeviceStatus,
      params: {},
      callback(res) {
        const { list, total, code } = res;
        msg.value = code == "00000" ? "请求成功" : "请求失败";
        pageRefData.value.section2.total = total;
        pageRefData.value.section2.list = markRaw(list);
      },
    },
  ],
  [
    "getDevicesData",
    {
      api_pro: getDevicesData,
      params: {
        current: 1,
        size: 10,
      },
      callback(handledData) {
        const { tableData, pageInfo } = handledData;
        setType(tableData);
        pageRefData.value.tip.tableData = markRaw(tableData);
        console.log(
          pageRefData.value.tip.tableData,
          "pageRefData.value.tip.tableData"
        );

        Object.assign(pageRefData.value.tip.paginationConfig, {
          currentPage: pageInfo.current,
          pages: pageInfo.pages,
          pageSize: pageInfo.size,
          total: pageInfo.total,
        });
      },
    },
  ],
  [
    "getTreeSelectData",
    {
      api_pro: getTreeSelectData,
      params: {},
      callback(selectData) {
        pageRefData.value.tip.condition.treeSelectOption = selectData;
      },
    },
  ],
  [
    "getDeviceTypeLabelMap",
    {
      api_pro: getDeviceTypeLabelMap,
      params: {},
      callback(typeOptions) {
        pageRefData.value.tip.condition.typeOptions = typeOptions;
      },
    },
  ],
]);
const cascaderRef = ref(null);
const handleChange = (value) => {
  cascaderRef.value.togglePopperVisible(false);
};
const apisMap2 = new Map([
  [
    "getDeviceData2",
    {
      api_pro: getDeviceData2,
      params: {
        objectId: "tonghenanlu_fenqu14",
      },
      callback(handledData) {
        const { tableData, pageInfo } = handledData;
        setType(tableData);
        pageRefData.value.tip.tableData = markRaw(tableData);
        Object.assign(pageRefData.value.tip.paginationConfig, {
          pages: pageInfo.pages,
          total: pageInfo.total,
        });
      },
    },
  ],
]);

const handleTip4Show = () => {
  const tip = pageRefData.value.tip;
  tip.visible = true;
};

const getData = async (flag) => {
  if (flag) {
    loading.value = true;
    await patchReq([...apisMap.values()]);
    loading.value = false;
  } else {
    await patchReq([...apisMap.values()]);
  }
};
let taskRef = null
onMounted(() => {
  getData();
  taskRef = ()=>getData(true)
  pollingManager.register(taskRef)
});
onUnmounted(() => {
  pollingManager.unregister(taskRef)
})
</script>

<style lang="less">
.el-cascader__dropdown.el-popper {
  background-color: #353f4d;
  transform-origin: left top;
  transform: scale(0.72);

  .el-cascader-node__label {
    color: #fff;
  }

  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background: #2d506f;
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #187db0;
    background: #187db0;
  }
}

.el-popper.is-light {
  transform-origin: left top;
  transform: scale(0.72);

  background-color: #353f4d;

  .el-select-dropdown__item {
    color: #ffffffe0;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover,
  .el-select-dropdown__item.selected {
    background: #2d506f;
  }

  .el-select-dropdown__item.selected {
    color: #3ebfff;
  }
}
</style>

<style lang="less" scoped>
:deep(.my-table .c-table-row) {
  &:hover {
    cursor: pointer;
  }
}

:deep(.cs-t1) {
  background-color: initial;
  padding: 0;
  // color:#dee7f1
}

.text-linear {
  background-image: -webkit-linear-gradient(bottom, #ffffff, #51e0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

:deep(.ct4 .c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    pointer-events: auto;
    height: 120px;

    &:hover {
      cursor: pointer;
    }

    background-color: #1581ce42;
  }
}
.ccc {
  :deep(.el-pagination) {
    color: #fff;

    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }

    .el-input__wrapper {
      background-color: #49779299;
    }

    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }

    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-select__icon {
      color: white;
    }

    .el-select__tags {
      top: 20%;

      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
:deep(.el-select__tags) {
  display: flex !important;
  max-width: none !important;
}
</style>
