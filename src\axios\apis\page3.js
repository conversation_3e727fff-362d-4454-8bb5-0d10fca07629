import request from "../request";
import dayjs from "dayjs";
import { markRaw } from "vue";
import { truncatedNumber } from "@/utils/num";
import { generateStyledChartOptions2 } from "../../utils";
// **决策驾驶仓分析接口**

export const dateFormat1 = "YYYY-MM";
export const dateFormat2 = "MM-DD";

export const getDateFormat = (day) => (day > 30 ? dateFormat1 : dateFormat2);

// 设备区域分析
export async function getDeviceAreaAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/device/analyse/deviceAreaAnalyse"
  );
  let total = 0;
  const handledData = res.data.map((item) => {
    const itemTotal = item.GJ + item.ZC;
    total += itemTotal;
    return {
      name: item.name,
      value: itemTotal,
    };
  });

  return { handledData, total, code: res.code };
}

// 设备运维分析
export async function getDeviceOperationAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/device/analyse/deviceOperationAnalyse",
    params
  );
  const keyLabelMap = new Map([
    ["yinhuan", "隐患"],
    ["weixiu", "维修"],
    ["gaojing", "告警"],
    ["richang", "日常"],
    ["baoyang", "保养"],
    ["xunjian", "巡检"],
  ]);
  const handledData = Object.entries(res.data).map(([key, value]) => ({
    name: keyLabelMap.get(key),
    value: value,
  }));
  handledData.code = res.code
  return handledData;
}

// 工单处理分析
export async function getWorkOrderAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/personnel/analyse/orderAnalyse",
    params
  );
  const keyLabelMap = new Map([
    ["daichuli", "待处理"],
    ["chaoshidaichuli", "超时待处理"],
    ["chaoshiyichuli", "超时已处理"],
    ["yichuli", "已完成"],
  ]);

  // handledData

  const handledData = res.data.map((item) => {
    return {
      time: dayjs(item.date).format("MM-DD"),
      val1: item.chaoshidaichuli,
      val2: item.chaoshiyichuli,
      val3: item.yichuli,
      val4: item.daichuli,
    };
  });
  handledData.code = res.code
  return handledData;
}

// 人员操作分析
export async function getPersonnelOperationAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/personnel/analyse/personnelOperationAnalyse",
    params
  );
  let total = res.data.reduce((acc, cur) => acc + cur.sum, 0);

  const handledData = res.data.map((item) => {
    const num = Math.round((item.sum * 100) / total)
    return {
      name: item.name,
      val: isNaN(num) ? "0%" : num + '%',
      detail: item["list"] || [],
    }
  });
  handledData.code = res.code
  return handledData;
}

// 告警同环比分析
export async function getAlarmComparisonAnalysis() {
  const currentMonth = dayjs(); // 当年当月
  const preMonth = currentMonth.subtract(1, "month"); // 当年前一个月

  const preYearCurrentMonth = currentMonth.subtract(1, "year"); // 去年当前月

  const params = {
    year: currentMonth.format("YYYY-MM"),
  };
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/alarmChain",
    params
  );

  const handledData = [
    {
      type: "环比",
      label: `${preMonth.format("YYYY-MM")} vs ${currentMonth.format(
        "YYYY-MM"
      )}`,
      val: res.data.onMonth.momStr,
      dir: res.data.onMonth.momStr.includes("-") ? "down" : "up",
    },
    {
      type: "同比",
      label: `${preYearCurrentMonth.format("YYYY-MM")} vs ${currentMonth.format(
        "YYYY-MM"
      )}`,
      val: res.data.onYear.momStr,
      dir: res.data.onYear.momStr.includes("-") ? "down" : "up",
    },
  ];
  console.log(handledData, '同环比');
  handledData.code = res.code
  return handledData;
}

// 告警设备Top5
export async function getAlarmDeviceTop5(params) {
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/getAlarmByDay",
    params
  );
  const total = res.data.reduce((acc, cur) => acc + cur.equipmentCount, 0);
  const handledData = res.data.map((item) => ({
    name: item.equipmentName,
    val: Math.round((item.equipmentCount * 100) / total) + "%",
    detail: [
      {
        label: "设备名称",
        value: item.equipmentName,
      },
      {
        label: "设备编码",
        value: item.equipmentCode,
      },
      {
        label: "告警次数",
        value: item.equipmentCount,
      },
      {
        label: "告警时间",
        value: item.alarmTime,
      },
    ],
  }));
  handledData.code = res.code
  return handledData;
}

// 当月告警处置分析
export async function getMonthAlarmDisposalAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/currentMonthAlarmAnalysis",
    params
  );
  const labelMap = new Map([
    ["处理中", "k1"],
    ["已处理", "k2"],
    ["未处理", "k3"],
  ]);
  const handledData = {};
  const optionData = [];
  const total = res.data.reduce((acc, cur) => acc + Number(cur.count), 0);
  res.data.forEach((item) => {
    const prop = labelMap.get(item.handleName);
    handledData[prop] = ((item.count * 100) / total).toFixed(2) + "%";
    handledData[prop + "_detail"] = item.infoVOS;    
    handledData['status' + prop] = item.handleStatus
    optionData.push({
      name: item.handleName,
      value: item.count,
    });
  });
  return { handledData, optionData, code: res.code };
}

// 近一年告警趋势分析
export async function getYearAlarmTrendAnalysis() {
  const currentDate = dayjs();
  const params = {
    startTime: currentDate.subtract(1, "year").format("YYYY-MM-DD"),
    endTime: currentDate.format("YYYY-MM-DD"),
  };
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/alarmDayCount",
    params
  );
  const handledData = res.data.map((item) => ({
    name: dayjs(item.alarmTime).format("MM-DD"),
    value: item.total,
  }));
  return { optionData: handledData, code: res.code };
}

// 当月告警分析 ai   参数可能是由UE传递，roadId和source
export async function getMonthAlarmAnalysisAI() {
  const params = {
    roadId: "dffab6c402da8be3c5f01f94e7900847",
    source: "2",
  };
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/alarmListByCabin",
    params
  );

  const handledData = res.data.map((item, index) => ({
    label: item.cabin,
    val: item.cabinNumber,
    key: "key" + index,
    detail: markRaw(item),
  }));
  handledData.code = res.code
  return handledData;
}

// 设备区域分析
export async function getAreaAlarmAnalysis() {
  const params = {
    roadId: "dffab6c402da8be3c5f01f94e7900847",
    source: "2",
  };
  const res = await request(
    "/admin/pipe/bigScreen/alarm/analyse/alarmRegionAnalysis",
    params
  );
  const handledData = res.data.map((item, index) => ({
    label: item.cabinName,
    val: item.cabinNumber,
    key: "k" + index,
    detail: markRaw(item),
  }));
  handledData.code = res.code
  return handledData;
}

// 入廊行为分析
export async function entranceApplybehaviorAnalyse(params) {
  const res = await request(
    "/admin/pipe/bigScreen/personnel/analyse/entranceApplybehaviorAnalyse",
    params
  );
  const handledData = res.data.map((item, index) => ({
    date: dayjs(item.date).format(getDateFormat(params.day)),
    val1: item.drrl,
    val2: item.rydd,
    val3: item.wgfpdaqm,
  }));
  handledData.code = res.code
  return handledData;
}

// 设备数据接入分析

export async function deviceAccessAnalyse(params) {
  const res = await request(
    "/admin/pipe/bigScreen/device/analyse/deviceAccessAnalyse",
    params
  );
  const dataMap = new Map([]);
  const _data = res.data || [];
  _data.forEach((item) => {
    dataMap.set(item.name, {
      name: item.name,
      value: item.value,
      list: (item.list || []).map((item2, index2) => ({
        k1: index2 + 1,
        k2: item2.name,
        k3: item2.value,
      })),
    });
  });
  dataMap.code = res.code
  return dataMap;
}

// 环境分析数据
export async function environmentAnalysisData(params) {
  const res = await request(
    "/admin/pipe/bigScreen/circumstances/analyse/deviceTypeQueriesdeviceRecordsWithinTheLast6Hours",
    {
      partitionCode: 'tonghenanlu_fenqu2',
      ...params
    },
    "POST"
  );
  const data = res.data || [];
  //  chartDataMap.code = res.code
  const options = generateStyledChartOptions2(data)
  options.code = res.code  
  return options
  // const chartDataMap = new Map([]);
  // const handledData = data.map((item) => {
  //   const ItemDevices = (item.devices || []).filter((item2) => {
  //     return Array.isArray((item2 || {}).recordData)
  //   }
  //   );    
  //   const isShowLines = ItemDevices.length > 0;
  //   const lineSeries = [];
  //   ItemDevices.forEach((item3) => {
  //     const lineData = item3.recordData.map((item4) => {
  //       const objs = Object.values(item4.params || {});
  //       const val = objs.length > 0 ? objs[0].value_real : 0;
  //       return [item4.dataTime, truncatedNumber(val)];
  //     });      
  //     lineSeries.push(lineData);
  //   });

  //   const chartDataItem = {
  //     ...item,
  //     isShowLines,
  //     lineSeries,
  //   };
  //   chartDataMap.set(chartDataItem.deviceTypeName, chartDataItem);
  // });
  // console.log(chartDataMap, 'chartDataMap');

  // return chartDataMap;
}