<template>
  <!-- <Teleport to="#app-main"> -->
  <div
    class="fixed inset-0 pointer-events-none z-20"
    :class="[center ? 'cus-flex-center' : '']"
  >
    <div
      class="absolute inset-0 z-[10] pointer-events-auto c-mask"
      :style="{
        backgroundColor: props.maskColor,
      }"
      v-if="showMask && visible"
    ></div>
    <div
      class="absolute rounded-[8px] p-[28px] whitespace-nowrap leading-relaxed text-[#CBDDF2FF] z-[10] pointer-events-auto"
      :class="[stl]"
      v-show="visible"
      ref="target"
    >
      <div
        class="e-close absolute top-[40px] right-[32px] bg-[url('@/assets/imgs/comp/p54.png')] w-[44px] h-[50px] cus-bg-full scale-[1.2] z-10 cus-use-click"
        v-show="close"
        @click="closeTipHandle"
      ></div>
      <slot></slot>
    </div>
  </div>
  <!-- </Teleport> -->
</template>

<script setup>
import { onClickOutside } from "@vueuse/core";
import { ref } from "vue";
const target = ref(null);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  close: {
    type: Boolean,
    default: false,
  },
  stl: {
    type: String,
    default: "",
  },
  showMask: {
    type: Boolean,
    default: false,
  },
  center: {
    type: Boolean,
    default: false,
  },
  autoClose: {
    type: Boolean,
    default: true,
  },
  isClose: {
    type: Boolean,
    default: true,
  },
  maskColor: {
    type: String,
    default: "rgba(0,0,0,0.1)",
  },
});
const emit = defineEmits(["update:visible","close-tip"]);

if (props.autoClose) {
  onClickOutside(target, (event) => {
    // 点击弹窗外部关闭弹窗
    // console.log("点击弹窗外部关闭弹窗");
    emit("update:visible", false);
    emit("close-tip");
  });
}

const closeTipHandle = () => {
  console.log("点击关闭按钮", props.isClose);
  if(props.isClose) {
     emit("update:visible", false);
  }
  emit("close-tip");
};
</script>

<style lang="less" scoped>
.c-mask {
  backdrop-filter: blur(0.5rem);
}
</style>
