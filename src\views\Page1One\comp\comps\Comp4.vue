<template>
  <div class="mt-[40px]">
    <PanelTitle v-loading="loading" :element-loading-text="msg">
      <div
        class="bg-cover bg-[url(@/assets/imgs/page1/p63.png)] bg-no-repate bg-[0%_0%] w-full h-[76px] relative"
      >
        <Refresh
          @click="
            getTableData(true, {
              current: paginationConfig.currentPage,
              size: paginationConfig.pageSize,
              query: {
                code: 'tonghenanlu_fenqu',
              },
            })
          "
          class="top-[30px] right-[150px]"
        />
      </div>
      <div
        class="bg-[url(@/assets/imgs/page1/p56-2.png)] bg-cover bg-no-repeat"
      >
        <div class="h-[848px] px-[56px] pt-[36px] mt-[2px]">
          <MyTable
            :titles="pageStaticData.section4.titles"
            :table-data="tableData"
            max-height="560px"
            gap="18px"
            dir="left"
            :autoScroll="tableData.length > 3"
            @item-click="handleClickSection4"
          >
            <template #k1="{ val }">
              <span
                class="inline-block bg-[#CBDDF2] w-[48px] h-[48px] cus-flex-center text-[#FFFFFF]"
              >
                {{ val }}
              </span>
            </template>

            <template #k2="{ val }">
              <div>{{ ellipsis(val, 10) }}</div>
            </template>

            <template #k3="{ val }">
              <div>{{ ellipsis(val, 10) }}</div>
            </template>
            <template #k6="{ val }">
              <span
                class="inline-block bg-[#CBDDF2] w-[48px] h-[48px] cus-flex-center"
                :class="pageStaticData.section4.handleWaning(val).class"
              >
                {{ pageStaticData.section4.handleWaning(val).label }}
              </span>
            </template>
          </MyTable>
          <div class="flex justify-center ccc">
            <el-pagination
              v-model:current-page="paginationConfig.currentPage"
              v-model:page-size="paginationConfig.pageSize"
              :page-sizes="paginationConfig.pageSizes"
              size="large"
              :disabled="false"
              :background="false"
              layout="total, sizes, prev, pager, next, jumper"
              :total="paginationConfig.total"
              @size-change="sizeChange"
              @current-change="currentChange"
              class="scale-[2] mt-[60px]"
              locale="zh-cn"
            />
          </div>
        </div>
      </div>
    </PanelTitle>
    <!-- </PanelTitle> -->

    <!-- 告警详情 -->
    <My-Tip2
      class="left-[2228px] top-[1678px]"
      v-model:visible="pageRefData.tips.t1.visible"
      @close-tip="pageRefData.tips.t1.closeHandle"
    >
      <div class="flex flex-col gap-y-[24px]">
        <div
          v-for="item in pageRefData.tips.t1.detail"
          :key="item.label"
          class="flex items-center odd:bg-[url('@/assets/imgs/page1/p101.png')] even:bg-[url('@/assets/imgs/page1/p102.png')] w-[716px] h-[64px] cus-bg-full"
        >
          <span class="w-[200px] shrink-0">{{ item.label }}：</span>
          <span v-if="item.label !== '图片'" class="truncate">
            <template v-if="item.label === '告警等级'">
              {{
                item.value !== null && item.value !== undefined
                  ? handleWaning(item.value)?.label
                  : "暂无数据"
              }}
            </template>
            <template v-else>
              {{
                item.value !== null && item.value !== undefined && item.value !== ''
                  ? item.value
                  : "暂无数据"
              }}
            </template>
          </span>
          <span v-else @click="handleImg(item)">
            {{ item.value ? "查看告警图片" : "暂无数据" }}
          </span>
        </div>
      </div>
      <div class="flex mt-[24px] items-center justify-around">
        <div
          class="cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center cus-use-click"
          v-for="item in pageRefData.tips.t1.opts"
          :key="item"
          @click="() => item.cb?.(item)"
        >
          <template v-if="item.label == '告警定位'">
            <el-tooltip
              v-if="pageRefData.tips.t1.detail.status == 'no'"
              content="当前设备不能定位"
              placement="top"
              effect="dark"
              teleported
            >
              <div :style="{ cursor: 'not-allowed', opacity: 0.5 }">
                {{ item.label }}
              </div>
            </el-tooltip>
            <span v-else>{{ item.label }}</span>
          </template>
          <template v-else>
            <span>{{ item.label }}</span>
          </template>
        </div>
      </div>
    </My-Tip2>
    <!-- 上报意见填写 -->
    <My-Tip2
      class="left-[3201px] top-[1678px]"
      stl="t2"
      title_class="tt2"
      v-model:visible="pageRefData.tips.t2.visible"
    >
      <div>
        <div class="mx-[18px]">
          <textarea
            v-model="pageRefData.tips.t2.detail.text"
            class="w-full h-[310px] outline-none bg-transparent bg-gradient-to-b from-[#1482CD66] to-[#1685CD33]"
          ></textarea>
        </div>
        <div class="flex mt-[24px] items-center mr-[20px]">
          <div
            v-for="item in pageRefData.tips.t2.opts"
            :key="item"
            class="cus-use-click cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center first:ml-auto last:ml-[24px]"
            @click="() => item.cb?.()"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </My-Tip2>

    <!-- 处理意见填写 -->
    <My-Tip2
      class="left-[3201px] top-[1678px]"
      stl="t3"
      title_class="tt3"
      v-model:visible="pageRefData.tips.t3.visible"
    >
      <div>
        <div class="mx-[18px]">
          <textarea
            v-model="pageRefData.tips.t3.detail.text"
            class="w-full h-[310px] outline-none bg-transparent bg-gradient-to-b from-[#1482CD66] to-[#1685CD33]"
          ></textarea>
        </div>
        <div class="flex mt-[24px] items-center mr-[20px]">
          <div
            v-for="item in pageRefData.tips.t3.opts"
            :key="item"
            class="cus-use-click cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center first:ml-auto last:ml-[24px]"
            @click="() => item.cb?.()"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </My-Tip2>
  </div>
  <el-dialog v-model="dialogVisible" width="40%" title="告警图片">
    <div class="flex justify-center my-[0px]">
      <el-image class="w-full" style="margin: auto" :src="imgUrl" />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, markRaw, onMounted, watch, onUnmounted } from "vue";
import { findPartitionAlarmInfo } from "@/axios/apis/page1";
import Refresh from "@/components/my-ui/Refresh.vue";
import { ellipsis } from "@/utils/dataHandle";
import { insertZhjkAlarmReport, addZhjkAlarmInfo } from "@/axios/apis/page2";
import patchReq from "@/utils/patchReq";
import { dayjs, ElMessage } from "element-plus";
import request from "@/axios/request";
import { useStore } from "vuex";
import pollingManager from "@/utils/pollingManager";
import { getJSONFromStorage, handleWaning } from "@/utils";
const tableData = ref([]);
const props = defineProps({
  flag: String,
});
const dialogVisible = ref(false);
const store = useStore();
const loading = ref(false);
const pageStaticData = {
  section4: {
    titles: [
      // {
      //   label: "序号",
      //   prop: "k1",
      //   dir: "center",
      //   width: 2,
      // },
      {
        label: "告警设备",
        prop: "k2",
        width: '400px',
      },
      {
        label: "告警区域",
        prop: "k3",
        width: 3.2,
      },
      {
        label: "告警详情",
        prop: "k4",
        width: 2.2,
      },
      {
        label: "告警时间",
        prop: "k5",
        width: 3.2,
      },
      {
        label: "告警等级",
        prop: "k6",
        width: 2,
      },
    ],

    handleWaning: (val) => {
      const map = new Map([
        ["0", { label: "高", class: "warning-heigh warning-base" }],
        ["1", { label: "中", class: "warning-medium warning-base" }],
        ["2", { label: "低", class: "warning-lower warning-base" }],
      ]);
      return (
        map.get(val + "") ?? {
          label: "低",
          class: "warning-lower warning-base",
        }
      );
    },
  },
};
const currentChange = (page) => {
  paginationConfig.value.currentPage = page;
};
const deviceCode = ref("");
const sizeChange = (size) => {
  paginationConfig.value.pageSize = size;
};
const pageRefData = ref({
  section4: {
    tableData: markRaw(
      Array.from({ length: 3 }, (_, index) => ({
        k1: index + 1,
        k2: "温感探头",
        k3: "规划一街综合舱1区",
        k4: "探头故障",
        k5: "2024-03-19 15:15:10",
        k6: index,
      }))
    ),
  },
  tips: {
    t1: {
      visible: false,
      detail: markRaw([
        // {
        //   label: "设备编号",
        //   prop: "deviceId",
        //   value: "0101REPF01",
        // },
        {
          label: "设备编码",
          prop: "equipmentCode",
          value: "",
        },
        {
          label: "设备名称",
          prop: "equipmentName",
          value: "01R01热力舱排风机",
        },
        {
          label: "设备位置",
          prop: "road",
          value: "北辰东道/1分区/热力舱",
        },
        {
          label: "告警分类",
          prop: "alarmClassify",
          value: "环控系统",
        },
        {
          label: "告警等级",
          prop: "alarmLevel",
          value: "",
        },
        {
          label: "告警描述",
          prop: "alarmDescription",
          value: "异常",
        },
        {
          label: "告警时间",
          prop: "alarmTime",
          value: "2024-04-17 16:00",
        },
        {
          label: "图片",
          prop: "img",
          value: "",
        },
      ]),
      index: 0,
      currentid: "",
      opts: [
        {
          label: "上报",
          cb() {
            const tips = pageRefData.value.tips;
            const cShow = tips.t2.visible;
            tips.t2.visible = !cShow;
            // 上报与处理不能同时存在
            if (tips.t3.visible && !cShow) {
              tips.t3.visible = false;
            }
          },
        },
        {
          label: "处理",
          cb() {
            const tips = pageRefData.value.tips;
            const cShow = tips.t3.visible;
            tips.t3.visible = !cShow;
            if (tips.t2.visible && !cShow) {
              tips.t2.visible = false;
            }
          },
        },
        {
          label: "告警定位",
          cb() {
            const tips = pageRefData.value.tips;
            const ids = [
              "1Z15EOX01",
              "1Z15ETH01",
              "1Z15ETH02",
              "1Z15ECH401",
              "1D01SCAM01",
              "1D01SCAM03",
              "1D02SCAM01",
            ];
            const params = {
              type: "web_device_status__change",
              data: {
                text: deviceCode.value,
              },
            };
            sendToUE5(params);
            pageRefData.value.tips.t1.visible = false
          },
        },
      ],
      closeHandle() {
        const tips = pageRefData.value.tips;
        tips.t1.visible = false;
        tips.t2.visible = false;
      },
    },
    t2: {
      visible: false,
      detail: {
        text: "",
      },
      opts: [
        {
          label: "取消",
          cb() {
            pageRefData.value.tips.t2.visible = false;
          },
        },
        {
          label: "确认",
          cb() {
            const tips = pageRefData.value.tips;
            const cid = tips.t1.currentid;
            const reason = tips.t2.detail.text;
            insertZhjkAlarmReport({
              alarmId: cid,
              reportBy: "system_admin",
              reportById: "c485a92a74fed468dcec144238d6c55c",
              reportTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
              reason: reason,
            }).then((res) => {
              res.code == "00000"
                ? ElMessage.success({
                    message: "上报意见提交成功",
                  })
                : ElMessage.error({
                    message: res.message,
                  });
              tips.t2.detail.text = "";
              tips.t2.visible = false;
            });
          },
        },
      ],
    },
    t3: {
      visible: false,
      detail: {
        text: "",
      },
      opts: [
        {
          label: "取消",
          cb() {
            pageRefData.value.tips.t3.visible = false;
          },
        },
        {
          label: "确认",
          cb() {
            const tips = pageRefData.value.tips;
            const cid = tips.t1.currentid;
            const reason = tips.t3.detail.text;
            addZhjkAlarmInfo({
              alarmId: cid,
              reason: reason,
            }).then((res) => {
              res.code == "00000"
                ? ElMessage.success({
                    message: "处理意见提交成功",
                  })
                : ElMessage.error({
                    message: res.message,
                  });
              tips.t3.detail.text = "";
              tips.t3.visible = false;
            });
          },
        },
      ],
    },
  },
});

// 进入页面时需要请求
const apisMap = new Map([
  [
    "findPartitionAlarmInfo",
    {
      api_pro: findPartitionAlarmInfo,
      params: {
        code: "tonghenanlu",
      },
      callback(handledData) {
        pageRefData.value.section4.tableData = markRaw(handledData);
      },
    },
  ],
]);
const msg = ref("");
const paginationConfig = ref({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 15, 30, 50],
  total: 80,
  disabled: false,
});
const getTableData = async (flag, params) => {
  flag && (loading.value = true);
  try {
    const _params = {
      current: paginationConfig.currentPage,
      size: paginationConfig.pageSize,
      query: {
        code: "tonghenanlu_fenqu",
        handleStatus :0
        // +
        // store.state.pageTwoCode[1] +
        // "_" +
        // store.state.pageTwoCode[0],
      },
      ...params,
    };
    const res = await request(
      "/admin/pipe/monitor/alarm/findZhjkAlarmInfo",
      _params,
      "POST"
    );
    paginationConfig.value.total = res.data.total;
    const data = res?.data?.records || [];
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
    tableData.value = data.map((i, index) => {
      return {
        ...i,
        k1: index + 1,
        k2: i.equipmentName,
        k3: i.partitionArea ?? "",
        k4: i.alarmDescription,
        k5: i.alarmTime,
        k6: i.alarmLevel ?? "",
        img: i.imgUrl,
        equipmentCode: i.equipmentCode,
      };
    });
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    loading.value = false;
  }
};
const imgUrl = ref("");
const handleImg = (item) => {
  if (!item.value) return;
  dialogVisible.value = true;
  imgUrl.value = item.value;
};
watch(
  () => [
    paginationConfig.value.currentPage,
    paginationConfig.value.pageSize,
    // store.state.pageTwoCode,
  ],
  () => {
    console.log(paginationConfig, "pageConfig ", store.state.pageTwoCode);
    getTableData(true, {
      size: paginationConfig.value.pageSize,
      current: paginationConfig.value.currentPage,
      // query: {
      //   code:
      //     "tonghenanlu_fenqu" +
      //     store.state.pageTwoCode[1] +
      //     "_" +
      //     store.state.pageTwoCode[0],
      // },
    });
  },
  {
    deep: true,
  }
);
const handleClickSection4 = ({ row, rindex }) => {
  let devices = getJSONFromStorage("devices");
  const tips = pageRefData.value.tips;
  const tData = tips.t1.detail.map((item) => {
    return {
      ...item,
      value: row ? row[item.prop] : "暂无数据",
    };
  });
  const info = tData.find((i) => i.prop == "equipmentCode");
  if (devices.length > 0) {
    const device = devices.find((i) => i == info?.value);
    if (device) {
      tData.status = "yes";
    } else {
      tData.status = "no";
    }
  }
  if (info) {
    deviceCode.value = info.value;
  }
  tips.t1.detail = markRaw(tData);
  tips.t1.visible = true;
  tips.t1.index = rindex;
  tips.t1.currentid = row.id;
  console.log(tips.t1.detail, "tips.t1");
};
onMounted(getTableData);

const getData = async (flag) => {
  if (flag) {
    loading.value = true;
    await patchReq([...apisMap.values()]);
    loading.value = false;
  } else {
    await patchReq([...apisMap.values()]);
  }
};
let taskRef = null;
onMounted(() => {
  taskRef = () => getTableData(true);
  pollingManager.register(taskRef);
});
onUnmounted(() => {
  pollingManager.unregister(taskRef);
});
</script>

<style lang="less" scoped>
:deep(.my-table .c-table-row) {
  &:hover {
    cursor: pointer;
  }
}

.bg-radius-gradient {
  background-image: radial-gradient(
    circle,
    #30303059 0%,
    #30303059 60%,
    #ffffff00 60%
  );
}

.warning-base {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 124px;
  height: 64px;
  color: white;
}

.warning-heigh {
  background-color: rgba(255, 34, 0, 0.15);
  color: #ff7184;
}

.warning-medium {
  background-color: rgba(255, 153, 0, 0.15);
  color: #fdff8b;
}

.warning-lower {
  background-color: rgba(0, 209, 255, 0.15);
  color: #00d1ff;
}
:deep(.el-pagination__total) {
  color: #fff !important;
}
.ccc {
  :deep(.el-pagination) {
    color: #fff;

    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }

    .el-input__wrapper {
      background-color: #49779299;
    }

    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }

    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-select__icon {
      color: white;
    }

    .el-select__tags {
      top: 20%;

      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
</style>
