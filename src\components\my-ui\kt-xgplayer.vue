<!-- <script setup>
import { ref, onMounted, useAttrs, watch, onBeforeMount } from "vue";
import "xgplayer/dist/index.min.css"; // 引入西瓜视频样式
import XGPlayer from "xgplayer";
import HlsPlugin from "xgplayer-hls";
import FlvPlugin from "xgplayer-flv";
const transcodingServerAddress = "127.0.0.1:1399"; // 转码服务器地址
const props = defineProps({
  url: {
    type: String,
    required: true,
  },
});
console.log(props,'props.url');

const attrs = useAttrs();

const playerElRef = ref(null);
const ktPlayerWrapElRef = ref(null);

let player = null;

// 定义播放器类型
const typePlayers = {
  hls: (el, url, config = {}) => {
    let hlsUrl = url;
    if ((url + "").startsWith("rtsp://")) {
      // 获取转码后的地址，赋值
    }

    return new XGPlayer({
      plugins: [HlsPlugin],
      el,
      url: hlsUrl,
      cssFullscreen: false,
      ...config,
    });
  },
  flv(el, url, config = {}) {
    return new XGPlayer({
      el,
      url,
      plugins: [FlvPlugin],
      ...config,
    });
  },
  default: (el, url, config = {}) => {
    return new XGPlayer({
      el,
      url,
      ...config,
    });
  },

  autoPlayer(el, url, config = {}) {    
    let player = null;
    if (/.m3u8$/.test(url)) {
      player = this.hls;
    } else if (
      /\.flv$/.test(url) ||
      url.startsWith("ws://") ||
      url.startsWith("wss://")
    ) {      
      return this.flv(el, url, config);
    } else {
      player = this.default;
    }
    return player(el, url, config);
  },
};

// 初始化播放器
const setPlayer = () => {
  const wrapEl = ktPlayerWrapElRef.value;
  const { clientWidth: width, clientHeight: height } = wrapEl;

  console.log("width", width, "height", height);

  const playerEl = playerElRef.value;
  let player = typePlayers.autoPlayer(playerEl, props.url, {
    lang: "zh",
    autoplayMuted: true,
    autoplay: true,
    width,
    height,
    ...attrs,
  });
  return player;
};

const destroyPlayer = () => {
  if (player) {
    player.destroy(); // 销毁播放器
    player = null; // 将实例引用置空
  }
};

watch(
  () => props.url,
  () => {
    if (player) {
      player.switchURL(props.url);
      player.pause();
    } else {
      player = setPlayer();
    }
  }
);

onMounted(() => {
  player = setPlayer();
});

onBeforeMount(() => {
  destroyPlayer();
});
</script>

<template>
  <div class="kt-xgplayer" ref="ktPlayerWrapElRef">
    <div ref="playerElRef" class="xgplayer-container"></div>
  </div>
</template>

<style>
.kt-xgplayer,
.xgplayer-container {
  height: 100%;
  width: 100%;
}
</style> -->
<script setup>
import { ref, onMounted, useAttrs, watch, onBeforeUnmount } from "vue";
import "xgplayer/dist/index.min.css";
import XGPlayer from "xgplayer";
import FlvPlugin from "xgplayer-flv";

const props = defineProps({
  url: {
    type: String,
    required: true,
  },
  autoplay: {
    type: Boolean,
    default: true,
  },
});

const attrs = useAttrs();

const playerElRef = ref(null);
const ktPlayerWrapElRef = ref(null);

let player = null;

// 初始化播放器（固定使用 flv）
const setPlayer = () => {
  const wrapEl = ktPlayerWrapElRef.value;
  const { clientWidth: width, clientHeight: height } = wrapEl;
  const playerEl = playerElRef.value;

  return new XGPlayer({
    el: playerEl,
    url: props.url,
    plugins: [FlvPlugin],
    lang: "zh",
    autoplayMuted: true,
    autoplay: props.autoplay,
    width,
    isLive: true,
    height,
    ...attrs,
  });
};

const destroyPlayer = () => {
  if (player) {
    player.destroy();
    player = null;
  }
};

// 监听 url 切换，重新初始化
watch(
  () => props.url,
  (newUrl) => {
    destroyPlayer();
    player = setPlayer();
  }
);

// 初始化播放器
onMounted(() => {
  player = setPlayer();
});

// 卸载组件前销毁播放器
onBeforeUnmount(() => {
  destroyPlayer();
});
</script>

<template>
  <div class="kt-xgplayer" ref="ktPlayerWrapElRef">
    <div ref="playerElRef" class="xgplayer-container">
      <slot></slot>
    </div>
  </div>
</template>

<style>
.kt-xgplayer,
.xgplayer-container {
  height: 100%;
  width: 100%;
}
</style>
