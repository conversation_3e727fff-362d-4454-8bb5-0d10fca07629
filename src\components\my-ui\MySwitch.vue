<template>
  <div
    class="flex items-center gap-x-[48px] pointer-events-auto cursor-pointer"
  >
    <div
      v-for="item in options"
      :key="item.key"
      class="flex items-center"
      @click="switchChangeHandle(item)"
    >
      <div
        class="w-[72px] h-[70px] cus-bg-full"
        :class="[item.key === currentType ? 'bg-p1-p45' : 'bg-p1-p46']"
      ></div>
      <div
        class="text-[36px] text-[#CBDDF299] -ml-[42px]"
        :style="{
          color: item.key === currentType ? '#CBDDF2FF' : '#CBDDF299',
        }"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  val: {
    type: String,
    default: "7",
  },
});
const emit = defineEmits(["switchChange"]);

const currentType = ref(props.val);

const switchChangeHandle = (item) => {
  if(item.key === currentType.value) return;
  currentType.value = item.key;
  item.callback?.();
  emit("switchChange", item);
};
</script>

<style lang="less" scoped>
.bg-p1-p46 {
  background-image: url("@/assets/imgs/page1/p46.png");
}
.bg-p1-p45 {
  background-image: url("@/assets/imgs/page1/p45.png");
}
</style>
