<template>
  <div>
    <My-Tip
      stl="cs-t1 top-[904px]"
      v-model:visible="pageRefData.tip.visible"
      show-mask
      close
      center
      :auto-close="false"
    >
      <div
        class="w-[2548px] h-[1738px] cus-bg-full bg-[url('@/assets/imgs/page1/p115.png')] ccc"
      >
        <div class="font-medium pt-[20px] pl-[72px]">
          <span
            class="text-linear text-[76px] font-bold family-Regular tracking-[4px] italic"
            >设备综合状态</span
          >
        </div>
        <div class="tts flex items-center mt-[40px] relative z-10 pl-[80px]">
          <kt-scale :scale="2.4">
            <el-select
              v-model="value2"
              multiple
              collapse-tags
              placeholder="状态"
              style="width: 240px"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </kt-scale>
          <kt-scale :scale="2.4" class="ml-[40px]">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="To"
              start-placeholder="Start date"
              end-placeholder="End date"
              popper-class="cus-date-picker-popper"
            />
          </kt-scale>
          <kt-scale :scale="2.4">
            <el-button
              color="#5491b4"
              style="color: white; margin-left: 20px"
              type="primary"
              @click="pageRefData.tip.searchHandle"
              >搜索</el-button
            ></kt-scale
          >
        </div>
        <div
          class="ct4 w-[full] h-[1248px] mt-[40px] pl-[64px] pr-[52px] text-[40px]"
        >
          <C-Table
            :titles="pageRefData.tip.titles"
            :table-data="pageRefData.tip.tableData"
            @item-click="pageRefData.tip.t4TableClickHandle"
            max-height="1200px"
            gap="28px"
          >
            <!-- <template #k6>
              <div
                class="w-[52px] h-[64px] cus-bg-full bg-[url('@/assets/imgs/page1/p117.png')]"
              ></div>
            </template> -->
          </C-Table>
        </div>
        <div class="flex justify-center">
          <el-pagination
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            :page-sizes="paginationConfig.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @size-change="pageRefData.tip.searchHandle"
            @current-change="pageRefData.tip.searchHandle"
            class="scale-[2.6] mt-[60px]"
            locale="zh-cn"
          />
        </div>
      </div>
    </My-Tip>
  </div>
</template>

<script setup>
import { ref, markRaw, onMounted, onBeforeUnmount } from "vue";
import { watchEvents, cancelEvents } from "@/META";
import { getDeviceRunHistoryData } from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";

import dayjs from "dayjs";

let deviceCode = "";

const options = [
  {
    value: "NORMAL",
    label: "正常",
  },
  {
    value: "OFFLINE",
    label: "脱机",
  },
  {
    value: "FAULT",
    label: "故障",
  },
  {
    value: "WARN",
    label: "警告",
  },
  {
    value: "DISABLE",
    label: "禁用",
  },
];

const value2 = ref(options.map((item) => item.value));

const dateRange = ref([
  new Date(Date.now() - 3600 * 1000 * 24 * 365),
  new Date(),
]);

const props = defineProps({
  flag: String,
});
const pageRefData = ref({
  tip: {
    visible: false,
    paginationConfig: {
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 15, 30, 50],
      total: 80,
      disabled: false,
    },
    titles: [
      {
        label: "序号",
        prop: "k1",
        dir: "center",
        width: 1,
      },
      {
        label: "状态更新时间",
        prop: "k2",
        width: 3,
      },
      {
        label: "状态",
        prop: "k3",
        width: 2,
      },
      {
        label: "描述",
        prop: "k4",
        width: 3,
      },
      {
        label: "状态持续时间",
        prop: "k5",
        width: 2,
      },
      //   {
      //     label: "定位",
      //     prop: "k6",
      //   },
    ],
    tableData: [],
    t4TableClickHandle(payload) {
      return;
      const { row = {} } = payload;
      const params = {
        type: "web_device_status__change",
        data: {
          text: row.deviceCode,
        },
      };
      sendToUE5(params);
    },
    searchHandle() {
      //   debugger;
      const paginationConfig = pageRefData.value.tip.paginationConfig;
      const payload = {
        i_current: paginationConfig.currentPage,
        i_size: paginationConfig.pageSize,
        deviceCode,
        deviceStatuses: [...value2.value],
        statusTimeFrom: dayjs(dateRange.value[0]).format("YYYY-MM-DD HH:mm:ss"),
        statusTimeTo: dayjs(dateRange.value[1]).format("YYYY-MM-DD HH:mm:ss"),
      };
      const reqItem = apisMap.get("getDeviceRunHistoryData");
      reqItem.api_pro(payload).then(reqItem.callback);
    },
  },
});

const paginationConfig = pageRefData.value.tip.paginationConfig;

const apisMap = new Map([
  [
    "getDeviceRunHistoryData",
    {
      api_pro: getDeviceRunHistoryData,
      params: {},
      callback(handledData) {
        const { tableData, pageInfo } = handledData;
        pageRefData.value.tip.tableData = markRaw(tableData);
        Object.assign(pageRefData.value.tip.paginationConfig, {
          currentPage: pageInfo.current,
          pages: pageInfo.pages,
          pageSize: pageInfo.size,
          total: pageInfo.total,
        });
      },
    },
  ],
]);
// onMounted(()=>{
//   patchReq([...apisMap.values()])

// })
const handleTip4Show = (visible, dCode) => {
  const tip = pageRefData.value.tip;
  tip.visible = visible;
  if (!visible) return;
  // 设置 deviceCode 再去请求数据
  deviceCode = dCode;

  const reqItem = apisMap.get("getDeviceRunHistoryData");
  const payload = {
    deviceCode,
    statusTimeFrom: dayjs(dateRange[0]).format("YYYY-MM-DD HH:mm:ss"),
    statusTimeTo: dayjs(dateRange[1]).format("YYYY-MM-DD HH:mm:ss"),
  };
  reqItem.api_pro(payload).then(reqItem.callback);
};

const events = {
  "ue-show-run-history-data": (payload = {}) => {
    const { visible = false, deviceCode = "1Z14ETH01" } = payload;
    handleTip4Show(visible, deviceCode);
  },
};

onMounted(() => {
  watchEvents(events);
});
onBeforeUnmount(() => {
  cancelEvents(events);
});
</script>

<style lang="less">
.el-cascader__dropdown.el-popper {
  background-color: #353f4d;
  transform-origin: left top;
  transform: scale(0.72);
  .el-cascader-node__label {
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background: #2d506f;
  }
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #187db0;
    background: #187db0;
  }
}

.el-popper.is-light {
  transform-origin: left top;
  transform: scale(0.72);

  background-color: #353f4d;
  .el-select-dropdown__item {
    color: #ffffffe0;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover,
  .el-select-dropdown__item.selected {
    background: #2d506f;
  }
  .el-select-dropdown__item.selected {
    color: #3ebfff;
  }
}
</style>

<style lang="less" scoped>
:deep(.my-table .c-table-row) {
  &:hover {
    cursor: pointer;
  }
}

:deep(.cs-t1) {
  background-color: initial;
  padding: 0;
  // color:#dee7f1
}

.text-linear {
  background-image: -webkit-linear-gradient(bottom, #ffffff, #51e0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

:deep(.ct4 .c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
  }
  .c-table-row {
    color: rgba(203, 221, 242, 1);
    pointer-events: auto;
    height: 120px;
    &:hover {
      cursor: pointer;
    }
    background-color: #1581ce42;
  }
}

.ccc {
  :deep(.el-pagination) {
    color: #fff;
    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }
    .el-input__wrapper {
      background-color: #49779299;
    }
    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }
    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;
    .el-input {
      background-color: transparent;
    }
    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }
    .el-input__inner {
      color: white;
      &::placeholder {
        color: white;
      }
    }
    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;
    .el-input {
      background-color: transparent;
    }
    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }
    .el-input__inner {
      color: white;
      &::placeholder {
        color: white;
      }
    }
    .el-select__icon {
      color: white;
    }
    .el-select__tags {
      //   top: 20%;
      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
</style>

<style lang="css">
#headlessui-listbox-button-1 {
  margin-bottom: 0px;
}
</style>

<style lang="less">
.el-date-editor {
  background-color: #5491b4 !important;
  color: white;

  .el-range__icon,
  .el-range-input,
  .el-range-separator,
  .el-range__close-icon {
    color: white !important;
  }
}
.cus-date-picker-popper,
.el-picker-panel__body-wrapper,
.el-date-range-picker,
.el-date-picker,
.el-picker-panel__content {
  background-color: #353f4d !important;
  color: white;
  border-color: #2a4d65 !important;
  .in-range {
    .el-date-table-cell {
      background-color: #2a4d65 !important;
    }
  }
  .start-date,
  .end-date {
    .el-date-table-cell > .el-date-table-cell__text {
      background-color: #5491b4;
    }
  }
  .el-popper__arrow {
  }

  .today {
    color: #2a4d65;
  }
}
.el-picker-panel__footer {
  background-color: #353f4d !important;
  .el-button {
    color: rgb(141, 141, 141) !important;
  }
}
// .el-date-picker__header,
// .el-date-picker__header-label {
//   color: white !important;
// }
</style>
