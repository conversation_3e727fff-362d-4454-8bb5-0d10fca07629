@tailwind base;
@tailwind components;
@tailwind utilities;


.value1 {
  background: linear-gradient(180deg, #FFFFFF 0%, #00FFF0 68%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.value2 {
  background: linear-gradient(180deg, #FFFFFF 0%, #00C2FF 68%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.warn-value {
  background: linear-gradient(180deg, #FFD4CD 0%, #FF2400 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}


.x-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.text1 {
  color: rgba(255, 255, 255, 0.72);
}

/* **************************************************************************************************************************** */
.bg-full {
  background-size: 100% 100%;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.cus-full {
  @apply w-full h-full
}

.cus-bg-full {
  @apply bg-no-repeat bg-full
}

.cus-border {
  @apply border border-red-500 border-solid
}

.cus-full-relative {
  @apply w-full h-full relative
}

.cus-flex-center {
  @apply flex justify-center items-center
}

.cus-use-click{
  @apply pointer-events-auto cursor-pointer
}