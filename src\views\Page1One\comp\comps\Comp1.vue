<template>
  <PanelTitle v-loading="loading" :element-loading-text="msg">
    <div class="bg-[url(@/assets/imgs/page1/p56.png)] bg-cover bg-no-repeat bg-position-[0%_0%] w-full h-[76px]"></div>
    <div
      class="h-[600px] bg-no-repeat bg-cover bg-[url(@/assets/imgs/page1/p56-2.png)] flex box-border px-[128px] pt-[126px] bottom-[56px] justify-around"
    >
      <div
        v-for="item in pageRefData.section1"
        :key="item.label"
        class="w-[266px] flex flex-col items-center"
      >
        <div
          class="w-[240px] h-[258px] cus-bg-full bg-[url('@/assets/imgs/page1/p36.png')] relative flex cus-flex-center"
        >
          <div
            class="absolute w-[76px] h-[76px] cus-bg-full top-[10px]"
            :style="{ backgroundImage: `url(${item.icon})` }"
          ></div>
        </div>
        <div class="text-[43px] -mt-[20px] text-[#CBDDF2]">
          {{ item.label }}
        </div>
        <div
          class="w-[354px] h-[93px] text-[46px] cus-bg-full bg-[url('@/assets/imgs/page1/p54.png')] mt-[20px] cus-flex-center flex"
        >
          {{ item.val }}{{ " " + item.uni }}
        </div>
      </div>
    </div>
    <Refresh
      @click="getData(true)"
      style="position: absolute"
      class="right-[70px] top-[30px]"
    />
  </PanelTitle>
</template>

<script setup>
import { ref, markRaw, onMounted, onUnmounted } from "vue";
import patchReq from "@/utils/patchReq";
import { getImg } from "@/utils/assets";
import { getRealTimeEvents, totalOverview } from "@/axios/apis/page1";
import Refresh from "@/components/my-ui/Refresh.vue";
import pollingManager from '@/utils/pollingManager.js'
const loading = ref(false);
const props = defineProps({
  flag: String,
});
const pageRefData = ref({
  section1: [
    {
      label: "建设投资",
      icon: getImg("page1/p91.png"),
      val: "1000",
      uni: "万元",
      key: "k1",
    },
    {
      label: "设备总数",
      icon: getImg("page1/p7.png"),
      val: "1000",
      uni: "个",
      key: "k2",
    },
    {
      label: "管廊长度",
      icon: getImg("page1/p3.png"),
      val: "1000",
      uni: "km",
      key: "k3",
    },
    {
      label: "入廊管线长度",
      icon: getImg("page1/p4.png"),
      val: "1000",
      uni: "km",
      key: "k4",
    },
  ],
});
const msg = ref('')
const apisMap = new Map([
  [
    "totalOverview", // 总体概览
    {
      api_pro: totalOverview,
      params: {},
      callback(handledData) {
        if (handledData.code !== 200) {
          msg.value = '加载失败'
        } else {
          msg.value = '加载成功'
        }
        pageRefData.value.section1 = markRaw(
          pageRefData.value.section1.map((item) => {
            item.val = handledData[item.key];
            return item;
          })
        );
      },
    },
  ],
]);

const getData = async (flag) => {
  if (flag) {
    loading.value = true;
    await patchReq([...apisMap.values()]);
    loading.value = false;
  } else {
    await patchReq([...apisMap.values()]);
  }
};
let taskRef = null
onMounted(() => {
  getData();
  taskRef = ()=>getData(true)
  pollingManager.register(taskRef)
});
onUnmounted(() => {
  pollingManager.unregister(taskRef)
})
</script>

<style lang="less" scoped>
</style>
