import dayjs from "dayjs";
import request from "../request";

// 智能巡检-消防系统检测、环境系统检测、通讯检测 - 有问题
export async function getInspectionData(params) {
  const res = await request(
    "/admin/pipe/monitor/system/type/getDeviceRoamList",
    params
  );  
  const handledData = {};
  const dataHandleMap = new Map([
    [
      "环控系统",
      (item) => {
        const deviceTypeNameLabelMap = new Map([
          [
            "氧含量检测仪",
            {
              label: "氧含量监测",
              des: "Oxygen content monitoring",
            },
          ],
          [
            "温湿度检测仪",
            {
              label: "湿温度监测",
              des: "Wet temperature monitoring",
            },
          ],
          [
            "甲烷气体检测仪",
            {
              label: "甲烷含量监测",
              des: "Methane content monitoring",
            },
          ],
          [
            "硫化氢气体检测仪",
            {
              label: "硫化氧监测",
              des: "Sulfide oxygen monitoring",
            },
          ],
          [
            "风机",
            {
              label: "风机监测",
              des: "Wind speed monitoring",
            },
          ],
          [
            "风机控制箱",
            {
              label: "风机控制箱监测",
              des: "Wind speed control box monitoring",
            },
          ],
          [
            "一般照明回路",
            {
              label: "照明回路监测",
              des: "General lighting circuit monitoring",
            },
          ],
          [
            "排水泵控制箱",
            {
              label: "排水泵控制箱监测",
              des: "Water pump control box monitoring",
            },
          ],
        ]);
        handledData.environmental_monitoring = item.list.map((item) => {          
          const mapItem = deviceTypeNameLabelMap.get(item.deviceTypeName) || {
            label: item.deviceTypeName,
            des: item.deviceTypeName,
          };
          if (!mapItem) {
            console.warn(`未找到设备类型：${item.deviceTypeName}`);
          }
          return {
            label: mapItem.label,
            val: item.normal,
            warn: item.warn,
            des: mapItem.des,
            id: item.deviceTypeId,
            detail: [
              {
                label: "设备编码",
                value: item.deviceCode,
              },
              {
                label: "设备名称",
                value: item.deviceTypeName,
              },
            ],
          };
        });
      },
    ],
    [
      "消防系统",
      (item) => {        
        const list = item?.list || [];
        const deviceNameLabelMap = new Map([
          [
            "防潮式点式感烟探测器",
            {
              label: "烟感探头",
              icon: "page2-p17",
            },
          ],
          ["手动报警器", { label: "手报", icon: "page2-p11" }],
          ["防火阀", { label: "防火阀", icon: "p99" }]
        ]);
        handledData.fire_fighting_monitoring = list.map((item) => {
          const mapItem = deviceNameLabelMap.get(item.deviceTypeName) || {
            label: item.deviceTypeName,
            icon: "page2-p11",
          };
          return {
            label: mapItem.label,
            icon_img: mapItem.icon,
            id: item.deviceTypeId,
            details: [
              {
                label: "正常",
                val: item.normal,
                icon_color: "#2EDFB0",
                status: 0
              },
              {
                label: "告警",
                val: item.warn,
                icon_color: "#FDFF8B",
                status:1
              },
              {
                label: "故障",
                val: "0",
                icon_color: "#FF7184",
                status:2
              },
            ],
          };
        });
      },
    ],
  ]);
  res.data.forEach((item) => {    
    dataHandleMap.get(item.systemName)?.(item)
  });
  handledData.code = res.code  
  return handledData;
}

// 通讯设备状态统计
export async function getCommunicationStatus(params = {}) {
  return request(
    "/admin/pipe/bigScreen/device/analyse/TXDeviceAccessAnalyse"
  );
}

// 告警详情列表
export async function AlarmgetdailyAlarmListInfo(params) {
  const res = await request(
    "/admin/pipe/monitor/alarm/panoramicViewOfPipeGallery/AlarmgetdailyAlarmListInfo",
    params,
    "POST"
  );
  const handledData = res.data.map((item, index) => {
    return {
      k1: index + 1,
      k2: item.equipmentName,
      k3: item.cabin,
      k4: item.alarmDescription,
      k5: item.alarmTime,
      k6: item.alarmLevel,
      detail: item,
    };
  });
  return handledData;
}

// 近七天自动巡检报告
export async function getSevenDayList(params) {
  const res = await request(
    "/admin/pipe/inspection/automatic/record/getSevenDayList",
    params,
  );  
  const handledData = res.data.records.map((item, index) => {
    return {
      k1: index + 1,
      k2: item.areaName,
      k3: item.realName,
      k4: item.reportResult,
      k5: item.createTime,
      detail: item,
    };
  });
  handledData.total = res.data.total
  return handledData;
}

// 自动巡检报告详情
export async function getInspectionRecordList(params = {}) {
  const res = await request(
    "/admin/pipe/inspection/automatic/record/selectRecordById",
    params
  );

  return res.data || {};
}

// 某条巡检报告的巡检列表
export async function getInspectionReportDetail(params) {
  const res = await request(
    "/gallery-inspection-server/admin/pipe/inspection/automatic/record/getById",
    params
  );
}

// 上报 报警意见
export async function insertZhjkAlarmReport(params = {}) {
  const defaultParams = {
    reason: "上报", //上报意见
    alarmId: "95e1270ee0ce889057a0ecaff39aa063", //告警id
    // reportBy: "system_admin", //上报人
    // reportById: "c485a92a74fed468dcec144238d6c55c", //上报人id
    reportTime: dayjs().format("YYYY-MM-DD HH:mm:ss"), //上报时间
  };
  return request(
    "/admin/pipe/monitor/zhjkAlarmReportDO/insertZhjkAlarmReport",
    {
      ...defaultParams,
      ...params,
    },
    "POST"
  );
}

// 处理 报警意见
export async function addZhjkAlarmInfo(params = {}) {
  const defaultParams = {
    alarmId: "58827c2a21a7143277e0722cca6190a1", //告警id
    reason: "处理", //处理意见
  };

  return request(
    "/admin/pipe/monitor/ZhjkAlarmMisinformation/addZhjkAlarmInfo",
    {
      ...defaultParams,
      ...params,
    },
    "POST"
  );
}
