* {
  margin: 0;
  user-select: none;
}

.dg.ac {
  z-index: 1 !important;
}

*::-webkit-scrollbar {
  padding-left: 20px;
  width: .3rem;
  background-color: rgba(220, 232, 255, 1);
}

*::-webkit-scrollbar-thumb {
  background: rgba(69, 123, 223, 1);
  border-radius: .5rem;
}

select,
select * {
  color: black;
}

.text-gradual {
  background-image: -webkit-linear-gradient(bottom, #c4dcfd, #d7e8ff, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.up-down-animate {
  animation: up-down 4s linear infinite alternate;
}

@keyframes up-down {
  33.3% {
    transform: translateY(-14px);
  }

  100% {
    transform: translateY(14px);
  }
}


.single-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* line-height: 元素高度; */
}

.el-dialog {
  --el-dialog-content-font-size: 100px !important;
  --el-message-close-size: 100px !important;
  --el-dialog-title-font-size: 70px !important;
  --el-dialog-margin-top: 600px !important;
}
.el-dialog__body{
  background-color: transparent !important;
}
.el-dialog__header {
  padding-top: 60px !important;
  margin-left: 50px;
}
.el-dialog__headerbtn {
  top: -2px !important;
  right: 56px !important;
}

#app {
  /* 图片缩放算法 */
  image-rendering: pixelated;
  --el-loading-spinner-size: 90px;
}






.animate-turn {
  animation: rotate-turn 12s linear infinite;
}

@keyframes rotate-turn {
  100% {
    transform: rotate(-1turn);
  }
}

.animate-turn2 {
  animation: rotate-turn-2 12s linear infinite;
}

@keyframes rotate-turn-2 {
  100% {
    transform: rotate(1turn);
  }
}

.animate-turn3 {
  animation: rotate-turn 3s linear infinite;
}

@keyframes rotate-turn {
  100% {
    transform: rotate(-1turn);
  }
}

.trans-25d-left {
  transform-style: preserve-3d;
  transform: perspective(1000px) rotateY(2deg);
}

.trans-25d-right {
  transform-style: preserve-3d;
  transform: perspective(1000px) rotateY(-2deg);
}