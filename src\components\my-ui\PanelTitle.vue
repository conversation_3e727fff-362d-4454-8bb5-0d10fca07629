<template>
  <div element-loading-background="rgba(122, 122, 122, 0.5)" class="cus-bg-full leading-none relative" :class="[bgClass]">
    <slot></slot>
  </div>
</template>

<script setup>
import { ref } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "", //数组时，可以切换
  },
});

const titleBgMap = new Map([
  // page1
  ["总体概览", "bg-p1-56"],
  ["设备状态分析", "bg-p1-57"],
  ["告警统计分析", "bg-p1-59"],
  ["实时事件", "bg-p1-62"],
  ["告警详情列表", "bg-p1-63"],
  ["工单分析", "bg-p1-64"],
  ["能耗统计分析", "bg-p1-66"],
  ["当前值班详情", "bg-p1-71"],
  ["视频监控", "bg-p1-72"],
  // page2
  ["消防系统监测", "bg-p2-48"],
  ["环境参数监测", "bg-p2-49"],
  ["通信监测", "bg-p2-50"],
  ["视频监控", "bg-p2-55"],
  ["告警详情列表2", "bg-p2-56"],
  ["近七天自动巡检报告列表", "bg-p2-57"],
  ["管廊可视化分析", "bg-p2-58"],

  // page3
  ["告警设备排名TOP5", "bg-p3-55"],
  ["告警分析", "bg-p3-58"],
  ["告警同环比分析", "bg-p3-57"],
  ["趋势分析3", "bg-p3-9"],
  ["当月告警处置分析", "bg-p3-61"],
  ["近一年告警趋势分析", "bg-p3-3"],
  ["人员操作排名TOP5", "bg-p3-4"],
  ["设备数据接入分析", "bg-p3-6"],
  ["工单分析3", "bg-p3-8"],
  ["设备区域分析", "bg-p3-7"],
  ["入廊行为分析", "bg-p3-11"],
  ["趋势分析32", "bg-p3-60"],
  ["设备运维分析", "bg-p3-10"],
]);

const bgClass = titleBgMap.get(props.title);
</script>

<style lang="less" scoped>
.bg-p1-56 {
  background-image: url("@/assets/imgs/page1/p56.png");
}
.bg-p1-57 {
  background-image: url("@/assets/imgs/page1/p57.png");
}
.bg-p1-59 {
  background-image: url("@/assets/imgs/page1/p59.png");
}
.bg-p1-62 {
  background-image: url("@/assets/imgs/page1/p62.png");
}
.bg-p1-63 {
  background-image: url("@/assets/imgs/page1/p63.png");
}
.bg-p1-64 {
  background-image: url("@/assets/imgs/page1/p64.png");
}
.bg-p1-66 {
  background-image: url("@/assets/imgs/page1/p66.png");
}
.bg-p1-71 {
  background-image: url("@/assets/imgs/page1/p71.png");
}
.bg-p1-72 {
  background-image: url("@/assets/imgs/page1/p72.png");
}



.bg-p2-48 {
  background-image: url("@/assets/imgs/page2/p48.png");
}
.bg-p2-49 {
  background-image: url("@/assets/imgs/page2/p49.png");
}
.bg-p2-50 {
  background-image: url("@/assets/imgs/page2/p50.png");
}
.bg-p2-55 {
  background-image: url("@/assets/imgs/page2/p55.png");
}
.bg-p2-56 {
  background-image: url("@/assets/imgs/page2/p56.png");
}
.bg-p2-57 {
  background-image: url("@/assets/imgs/page2/p57.png");
}
.bg-p2-58 {
  background-image: url("@/assets/imgs/page2/p58.png");
}



.bg-p3-55 {
  background-image: url("@/assets/imgs/page3/p55.png");
}
.bg-p3-58 {
  background-image: url("@/assets/imgs/page3/p58.png");
}
.bg-p3-57 {
  background-image: url("@/assets/imgs/page3/p57.png");
}
.bg-p3-9 {
  background-image: url("@/assets/imgs/page3/p9.png");
}
.bg-p3-61 {
  background-image: url("@/assets/imgs/page3/p61.png");
}
.bg-p3-3 {
  background-image: url("@/assets/imgs/page3/p3.png");
}
.bg-p3-4 {
  background-image: url("@/assets/imgs/page3/p4.png");
}
.bg-p3-6 {
  background-image: url("@/assets/imgs/page3/p6.png");
}
.bg-p3-8 {
  background-image: url("@/assets/imgs/page3/p8.png");
}
.bg-p3-7 {
  background-image: url("@/assets/imgs/page3/p7.png");
}
.bg-p3-11 {
  background-image: url("@/assets/imgs/page3/p11.png");
}
.bg-p3-60 {
  background-image: url("@/assets/imgs/page3/p60.png");
}
.bg-p3-10 {
  background-image: url("@/assets/imgs/page3/p10.png");
}








</style>
