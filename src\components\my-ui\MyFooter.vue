<template>
  <div
    class="my-left-nav text-white family-JinBuTi relative flex cus-flex-center"
  >
    <!-- 背景图片 -->
    <div
      class="w-full h-[264px] cus-bg-full bg-[url('@/assets/imgs/page2/p59.png')] absolute bottom-0"
    ></div>
    <!-- 按钮 -->
    <div class="absolute w-full bottom-[84px] flex -gap-x-[4px] justify-center">
      <div
        v-for="item in pageStaticData.section1"
        :key="item.label"
        class="w-[454px] h-[304px] cus-bg-full hover:cursor-pointer first:rotate-[-10deg] last:rotate-[10deg]"
        :class="[
          pageRefData.section1 === item.label ? item.selected_img : item.img,
        ]"
        @click="changeSection1Handle(item.label)"
      ></div>
    </div>
    <div
      class="absolute w-[2704px] bottom-[316px] text-[60px] justify-center flex gap-x-[40px]"
    >
      <div
        v-for="i in 15"
        :key="i"
        class="w-[142px] h-[136px] flex cus-flex-center text-[48px] font-semibold relative text-[#fff]"
        :style="{
          transform: `translateY(${-Math.abs(i - 8) * 8}px)`,
        }"
      >
        <span
          class="tracking-[4px] absolute w-full h-full cus-flex-center hover:cursor-pointer"
          @click="changeSection2Handle(transformPartition(i))"
          @mouseenter="partitionHoverHandle('enter', transformPartition(i))"
          @mouseleave="partitionHoverHandle('leave', transformPartition(i))"
          >{{ transformPartition(i) }}</span
        >
        <div
          class="absolute w-full h-full bg-[#4963697b] -z-10"
          :class="[
            transformPartition(i) == pageRefData.section2
              ? 'bg-607C97AF'
              : 'bg-4963697B',
          ]"
          :style="{
            transform: `skewY(${8 - i}deg)`,
          }"
        ></div>
      </div>
    </div>
    <div
      class="absolute right-[2266px] bottom-[700px] flex flex-col gap-y-[20px]"
    >
      <template v-for="item in pageRefData.section4" :key="item.label">
        <div
          v-show="item.show"
          class="text-[52px] font-semibold cus-use-click cus-flex-center w-[284px] h-[106px] cus-bg-full bg-[url('@/assets/imgs/comp/p67.png')] hover:bg-[url('@/assets/imgs/comp/p68.png')]"
          @click="() => section4ItemClickHandle(item)"
        >
          <span class="text-shadow1">{{ item.label }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, markRaw, onBeforeUnmount, watch, computed } from "vue";
import { getImg } from "@/utils/assets";
import { getTreeData, getVideoMonitorData } from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";
import META, { watchEvents, cancelEvents } from "@/META.js";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
const videoList = ref([]);
const store = useStore();
const props = defineProps({});
const router = useRouter();
const emits = defineEmits(["change"]);
const pageStaticData = {
  section1: [
    {
      label: "电力舱",
      img: "comp-p1",
      selected_img: "comp-p2",
    },
    {
      label: "综合舱",
      img: "comp-p53",
      selected_img: "comp-p52",
    },
  ],
};
const pageRefData = ref({
  section1: "综合舱",
  section2: "01",
  section3: {},
  section4: [
    {
      label: "查看高度",
      show: false,
      callback: () => {
        console.log("查看高度");
      },
    },
    {
      label: "查看廊内",
      show: false,
      callback: () => {
        console.log("查看廊内");
        META.value.corridor_side = "廊内";
        if (router.currentRoute.value.path !== "/Page2Two") {
          router.push("/Page2Two");
        }
        META.bus.emit("web_menus2_change", { status: "status2" });
      },
    },
    {
      label: "查看廊外",
      show: false,
      callback: () => {
        META.value.corridor_side = "廊外";
        if (router.currentRoute.value.path !== "/Page2Two") {
          router.push("/Page2Two");
        }
        META.bus.emit("web_menus2_change", { status: "status3" });
      },
    },
  ],
});

const METAValue = META.value;

// 舱室切换
const changeSection1Handle = (label) => {
  // if (pageRefData.value.section1 == label) return;
  console.log(label, "舱室");

  pageRefData.value.section1 = label;
  METAValue.cabin = label;
  // 判断是否需要跳转到智慧巡检页面
  // if (METAValue.current_path == "/Page1One") {
  //   router.push("/Page2Two");
  //   METAValue.partition = METAValue.partition || "01";
  //   pageRefData.value.section2 = METAValue.partition;
  // }
  const params = {
    type: "cabin_change",
    data: {
      text: label,
    },
  };

  if (!METAValue.partition) {
    pageRefData.value.section2 = METAValue.partition = "01";
  }

  sendToUE5(params);

  // 触发舱室切换事件 让其更新面板数据
  console.log("触发舱室切换事件 让其更新面板数据");
  META.bus.emit("web_cabin_change", {
    cabin: label,
    partition: METAValue.partition,
  });
};

// 分区切换
const changeSection2Handle = (num) => {
  console.log(num, "当前分区");

  // if (pageRefData.value.section2 == num) return;
  pageRefData.value.section2 = num;
  METAValue.partition = num;
  const params = {
    type: "partition_focus",
    data: {
      text: num,
    },
  };
  sendToUE5(params);
  // 触发分区切换事件 让其更新面板数据
  console.log("触发分区切换事件 让其更新面板数据");
  META.bus.emit("web_partition_change", {
    cabin: METAValue.cabin,
    partition: METAValue.partition,
  });

  // 判断是否需要跳转到智慧巡检页面
  if (METAValue.current_path == "/Page1One") {
    // router.push("/Page2Two");
    META.bus.emit("web_menus2_change", {
      status: "status1",
    });
  }
};

// 分区鼠标悬浮交互
const partitionHoverHandle = (type, num) => {
  // 当在第一个页面时，才触发该事件
  if (META.value.mode !== "运行态势") return;

  const params = {
    type: "partition_hover",
    data: {
      type: type,
      text: num,
    },
  };
  sendToUE5(params);
  // console.log("分区鼠标悬浮交互", params);
};

const section4ItemClickHandle = (item) => {
  item.callback?.();
  const params = {
    type: "web_corridor_side_change",
    data: {
      text: item.label,
    },
  };
  sendToUE5(params);
};

//
const transformPartition = (num) => (num + "").padStart(2, "0");

const apisMap = new Map([
  [
    "getTreeData",
    {
      api_pro: getTreeData,
      params: {},
      callback(handledData) {
        pageRefData.value.section3 = markRaw(handledData); // 先把这个数据存着
        // 存到全局变量中
        setTreeData(handledData);
      },
    },
  ],
]);

// 设置存储分区分仓数据
function setTreeData(treeData) {
  window.__cabinTreeData = treeData;
  // 根据舱室，分区，获取对应的信息
  window.getCabinInfo = (partition, cabin) => {
    if (partition === undefined) return _cabinTreeData[0];
    const partitionInfo =
      __cabinTreeData[0].children[0].children[+partition - 1];
    if (cabin === undefined) return partitionInfo;
    if (partitionInfo) {
      return partitionInfo.children.find(
        (item) => item.name == partitionInfo.name + cabin
      );
    }
  };
}

const getData = () => {
  patchReq([...apisMap.values()]);
};

const events = {
  ue_partition_change: (data) => {
    const { partition, cabin } = data;
    pageRefData.value.section2 = transformPartition(partition);
    pageRefData.value.section1 = cabin;
    META.bus.emit("web_partition_change", {
      cabin: cabin,
      partition: partition,
    });
  },
  web_menus2_change: (data) => {
    const { status } = data;

    const statusMap = {
      status0: [],
      status1: ["查看廊内", "查看廊外"],
      status2: ["查看廊外"],
      status3: ["查看高度", "查看廊内"],
    };
    const showStatusLabels = statusMap[status];
    if (showStatusLabels) {
      pageRefData.value.section4.forEach((item, index) => {
        item.show = showStatusLabels.includes(item.label);
      });
    }
  },
  header_change_partition: (data) => {
    console.log("header_change_partition--->", data);
    const { partition } = data;
    pageRefData.value.section2 = transformPartition(partition);
  },
};

onMounted(() => {
  getData();
  watchEvents(events);
});
watch(
  () => [pageRefData.value.section1, pageRefData.value.section2],
  (val) => {
    store.commit("UPDATE_PageTwoCode", val);
  }
);
watch(
  () => META.value.partition,
  () => {
    pageRefData.value.section2 = transformPartition(META.value.partition);
  }
);

onBeforeUnmount(() => {
  console.log("取消监听");
  cancelEvents(events);
});
</script>

<style lang="less" scoped>
.comp-p53 {
  background-image: url("@/assets/imgs/comp/p53.png");
}
.comp-p52 {
  background-image: url("@/assets/imgs/comp/p52.png");
}

.comp-p1 {
  background-image: url("@/assets/imgs/comp/p1.png");
}

.comp-p2 {
  background-image: url("@/assets/imgs/comp/p2.png");
}

.bg-607C97AF {
  background-color: #607c97af;
}
.bg-4963697B {
  background-color: #4963697b;
}

.text-shadow1 {
  text-shadow: 0px 4px 12px rgba(27, 24, 19, 0.61);
}

// .comp-p67{
//   background-image: url('@/assets/imgs/comp/p67.png');
// }
// .comp-p68{
//   background-image: url('@/assets/imgs/comp/p68.png');
// }
</style>
