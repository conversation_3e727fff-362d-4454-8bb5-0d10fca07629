<template>
  <div class="text-[40px] flex justify-between">
    <div class="w-[880px]">
      <PanelTitle
        v-loading="loading1"
        title="人员操作排名TOP5"
        :element-loading-text="msg"
      >
        <div class="h-[546px] pt-[130px] px-[56px]">
          <div
            class="flex flex-col gap-y-[20px] h-full justify-around pb-[56px]"
          >
            <div
              v-for="(item, index) in pageRefData.section1"
              :key="item.name"
              class="flex items-center h-[54px] w-full cus-use-click"
              @click="handlers.h1(item)"
            >
              <span
                class="w-[54px] h-full bg-[url('@/assets/imgs/page3/p23.png')] cus-bg-full cus-flex-center text-[32px] font-medium text-[#FAF1E4FF]"
                >{{ index + 1 }}</span
              >
              <div
                class="ml-[6px] h-full w-[572px] bg-[url('@/assets/imgs/page3/p15.png')] cus-bg-full flex items-center"
              >
                <span
                  class="w-[220px] inline-block text-[#CBDDF2CC] text-[36px] ml-[16px]"
                >
                  {{ item.name }}
                </span>
                <div class="h-[16px] grow mx-[20px] flex items-center">
                  <div
                    class="w-[50%] h-full bg-gradient-to-r from-[#355090FF] to-[#608AF3FF]"
                    :style="{
                      width: `${item.val}`,
                    }"
                  ></div>
                  <span class="w-[6px] h-[28px] mr-auto bg-[#CBDDF2FF]"></span>
                </div>
              </div>
              <span
                class="w-[124px] ml-auto text-[#CBDDF2FF] font-medium h-full cus-flex-center h-full bg-[url('@/assets/imgs/page3/p56.png')] cus-bg-full text-[36px]"
                >{{ item.val }}</span
              >
            </div>
          </div>
        </div>
        <Refresh @click="getData('l1')" class="right-[100px] top-[35px]" />
      </PanelTitle>
      <PanelTitle v-loading="loading2" title="设备区域分析" class="mt-[18px]" :element-loading-text="msg">
        <div class="h-[564px] pt-[126px] px-[56px]">
          <div class="flex items-center">
            <div
              class="w-[354px] h-[368px] rounded-full bg-radius-gradient ml-[32px] cus-flex-center"
            >
              <div class="w-full h-[354px]">
                <Echart :option="pageRefData.options.option4" />
              </div>
            </div>
            <div class="flex flex-col gap-y-[12px] ml-auto">
              <div
                v-for="item in pageRefData.section3"
                :key="item.label"
                class="flex items-center w-[394px] h-[60px] bg-[url('@/assets/imgs/page1/p60.png')] cus-bg-full"
              >
                <div
                  class="w-[14px] h-[14px] rounded-full ml-[26px]"
                  :style="{ backgroundColor: item.icon_color }"
                ></div>
                <div
                  class="text-[32px] text-[#CBDDF2] ml-[16px] whitespace-nowrap truncate"
                >
                  {{ item.label }}
                </div>
                <div
                  class="text-[36px] text-[#CBDDF2] ml-auto font-medium mr-[16px]"
                >
                  {{ item.val }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <Refresh @click="getData('l2')" class="right-[100px] top-[35px]" />
      </PanelTitle>
      <PanelTitle v-loading="loading3" title="趋势分析32" class="mt-[38px]" :element-loading-text="msg">
        <div class="h-[1578px] pt-[126px] px-[56px]">
          <!-- 甲烷趋势分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p63.png')] cus-bg-full"
          ></div>
          <div class="w-full h-[580px] mt-[30px]">
            <Echart :option="pageRefData.options.option1" />
          </div>
          <!-- 硫化氧趋势分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p64.png')] cus-bg-full mt-[32px]"
          ></div>
          <div class="w-full h-[580px] mt-[30px]">
            <Echart :option="pageRefData.options.option2" />
          </div>
          <!-- 液位趋势分析 -->
          <!-- <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p65.png')] cus-bg-full mt-[32px]"
          ></div>
          <div class="w-full h-[340px] mt-[30px]">
            <Echart :option="pageRefData.options.option3" />
          </div> -->
        </div>
        <Refresh @click="getEnv(true)" class="right-[100px] top-[35px]" />
      </PanelTitle>
    </div>
    <div class="w-[880px]">
      <PanelTitle v-loading="loading4" title="设备数据接入分析" :element-loading-text="msg">
        <div class="h-[660px] pt-[110px] px-[56px]">
          <!-- <MySwitch
            @switchChange="
              (item) => handleSwitchChange(item, 'deviceAccessAnalyse')
            "
            :options="pageStaticData.section2.ops"
            val="7"
            class="justify-center"
          /> -->
          <div
            class="flex flex-wrap justify-between w-full gap-y-[64px] mt-[90px]"
          >
            <div
              v-for="item in pageRefData.section2.viewData"
              :key="item.name"
              class="flex basis-2/5 items-center cus-use-click"
              @click="handlers.h4(item)"
            >
              <div
                class="w-[134px] h-[144px] bg-[url('@/assets/imgs/page3/p22.png')] cus-bg-full cus-flex-center relative"
              >
                <div
                  class="absolute w-[60px] h-[72px] cus-bg-full top-0"
                  :style="{
                    backgroundImage: `url(${item.icon_img})`,
                  }"
                ></div>
              </div>
              <div class="flex flex-col items-center">
                <span class="text-[#CBDDF2FF]">{{ item.name }}</span>
                <div
                  class="w-[212px] h-[66px] bg-[url('@/assets/imgs/page3/p51.png')] cus-bg-full cus-flex-center text-[32px] mt-[10px] relative"
                >
                  <div class="absolute top-[8px]">
                    <span>{{ item.val }}</span>
                    <span>条</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Refresh @click="getData('l4')" class="right-[100px] top-[35px]" />
      </PanelTitle>
      <PanelTitle v-loading="loading5" title="工单分析3" class="mt-[1px]" :element-loading-text="msg">
        <div class="h-[700px] pt-[110px] px-[56px]">
          <MySwitch
            :options="pageStaticData.section2.ops"
            val="7"
            class="justify-center"
            @switchChange="
              (item) => handleSwitchChange(item, 'getWorkOrderAnalysis')
            "
          />
          <div class="h-[418px] mt-[32px] cus-use-click" @click="handlers.h3">
            <Echart :option="pageRefData.options.option5" />
          </div>
        </div>
        <Refresh @click="getData('l5')" class="right-[100px] top-[35px]" />
      </PanelTitle>
      <PanelTitle v-loading="loading6" title="入廊行为分析" class="mt-[38px]" :element-loading-text="msg">
        <div class="h-[674px] pt-[110px] px-[56px]">
          <MySwitch
            :options="pageStaticData.section2.ops"
            val="7"
            class="justify-center"
            @switchChange="
              (item) => handleSwitchChange(item, 'entranceApplybehaviorAnalyse')
            "
          />
          <div class="h-[422px] mt-[32px] cus-use-click" @click="handlers.h2">
            <Echart :option="pageRefData.options.option6" />
          </div>
        </div>
        <Refresh @click="getData('l6')" class="right-[100px] top-[35px]" />
      </PanelTitle>
      <PanelTitle v-loading="loading7" title="设备运维分析" class="mt-[48px]" :element-loading-text="msg">
        <div class="h-[616px] pt-[126px] px-[56px]">
          <MySwitch
            :options="pageStaticData.section2.ops"
            val="7"
            class="justify-center"
            @switchChange="
              (item) => handleSwitchChange(item, 'getDeviceOperationAnalysis')
            "
          />
          <div class="h-[318px] mt-[32px]">
            <Echart :option="pageRefData.options.option7" />
          </div>
        </div>
        <Refresh @click="getData('l7')" class="right-[100px] top-[35px]" />
      </PanelTitle>
    </div>
    <!-- 人员操作排名top5提示框 -->
    <My-Tip
      stl="right-[2229px] top-[456px]"
      v-model:visible="pageRefData.tips.tip1.visible"
    >
      <div class="w-[546px] h-[308px]">
        <C-Table
          :titles="pageRefData.tips.tip1.titles"
          :table-data="pageRefData.tips.tip1.tableData"
        ></C-Table>
      </div>
    </My-Tip>

    <!-- 入廊行为分析提示框 -->
    <My-Tip
      stl="right-[2155px] top-[1900px]"
      v-model:visible="pageRefData.tips.tip2.visible"
    >
      <div class="w-[864px] h-[374px]">
        <C-Table
          :titles="pageRefData.tips.tip2.titles"
          :table-data="pageRefData.tips.tip2.tableData"
        ></C-Table>
      </div>
    </My-Tip>

    <!-- 工单分析提示框 -->
    <My-Tip
      stl="right-[2120px] top-[1200px] ct-1"
      v-model:visible="pageRefData.tips.tip3.visible"
    >
      <div class="w-[546px] h-[308px]">
        <C-Table
          :titles="pageRefData.tips.tip3.titles"
          :table-data="pageRefData.tips.tip3.tableData"
        ></C-Table>
      </div>
    </My-Tip>

    <!-- 设备数据接入分析 -->
    <My-Tip
      stl="right-[2229px] top-[456px]"
      v-model:visible="pageRefData.tips.tip4.visible"
    >
      <div class="w-[886px] h-[308px]">
        <C-Table
          :titles="pageRefData.tips.tip4.titles"
          :table-data="pageRefData.tips.tip4.tableData"
          max-height="260px"
          :auto-scroll="pageRefData.tips.tip4.tableData.length > 4"
        ></C-Table>
      </div>
    </My-Tip>
  </div>
</template>

<script setup>
import { getImg } from "@/utils/assets";
import {
  createChartOp1,
  createChartOp4,
  createChartOp5,
  createChartOp6,
  createChartOp7,
} from "./chart-options/chart1";
import { ref, onMounted, markRaw, reactive,watch, onUnmounted } from "vue";
import pollingManager from '@/utils/pollingManager'
import {
  getDeviceAreaAnalysis,
  getDeviceOperationAnalysis,
  getWorkOrderAnalysis,
  getPersonnelOperationAnalysis,
  entranceApplybehaviorAnalyse,
  deviceAccessAnalyse,
  environmentAnalysisData
} from "@/axios/apis/page3";
import patchReq from "@/utils/patchReq";
import Refresh from "@/components/my-ui/Refresh.vue";
import { useStore } from "vuex";
const props = defineProps({
  // props
});
const setOption = (res) => {  
  // options.option3 = res["甲烷检测仪"];
  options.option2 = res["硫化氢气体检测仪"];
  options.option1 = res["甲烷气体检测仪"];
};
const pageStaticData = {
  section2: {
    ops: [
      {
        label: "近七天",
        key: "7",
      },
      {
        label: "近30天",
        key: "30",
      },
      {
        label: "近半年",
        key: "180",
      },
    ],
  },
};
const msg = ref("");
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const loading4 = ref(false);
const loading5 = ref(false);
const loading6 = ref(false);
const loading7 = ref(false);
const pageRefData = ref({
  options: {
    option1: {},
    option2: {},
    option3: {},
    option4: {},
    option5: {},
    option6: {},
    option7: {},
  },
  section1: markRaw([
    {
      name: "隐患申请",
      val: "20%",
    },
    {
      name: "维修申请",
      val: "27%",
    },
    {
      name: "物料申请",
      val: "15%",
    },
    {
      name: "入廊申请",
      val: "37%",
    },
  ]),
  section2: {
    viewData: markRaw([
      {
        icon_img: getImg("page3/p21.png"),
        val: "1000",
        name: "通信系统",
        key: "k1",
        list: [],
      },
      {
        icon_img: getImg("page3/p17.png"),
        val: "1000",
        name: "消防系统",
        key: "k2",
        list: [],
      },
      {
        icon_img: getImg("page3/p18.png"),
        val: "1000",
        name: "安防系统",
        key: "k3",
        list: [],
      },
      {
        icon_img: getImg("page3/p19.png"),
        val: "1000",
        name: "环控系统",
        key: "k4",
        list: [],
      },
    ]),
  },
  section3: markRaw([
    {
      label: "电力舱",
      icon_color: "rgba(244, 217, 130, 1)",
      val: "49.68%",
    },
    {
      label: "综合舱",
      icon_color: "rgba(232, 141, 107, 1)",
      val: "44.52%",
    },
    {
      label: "人员出入口",
      icon_color: "rgba(94, 137, 238, 1)",
      val: "0.65%",
    },
    {
      label: "进风口",
      icon_color: "rgba(97, 226, 157, 1)",
      val: "2.58%",
    },
    {
      label: "排风口",
      icon_color: "rgba(230, 126, 34,1.0)",
      val: "2.58%",
    },
    {
      label: "逃生口",
      icon_color: "rgba(26, 188, 156,1.0)",
      val: "0.00%",
    },
  ]),
  tips: {
    tip1: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 1.2,
        },
        {
          label: "账号名称",
          prop: "k2",
          width: 2,
        },
        {
          label: "使用次数",
          prop: "k3",
          width: 2,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "admin",
          k3: "2",
        },
        {
          k1: "2",
          k2: "user1",
          k3: "3",
        },
        {
          k1: "3",
          k2: "user2",
          k3: "3",
        },
      ]),
    },
    tip2: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 1.2,
        },
        {
          label: "告警区域",
          prop: "k2",
          width: 2,
        },
        {
          label: "告警设备",
          prop: "k3",
          width: 2,
        },
        {
          label: "告警时间",
          prop: "k4",
          width: 4,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "综合舱",
          k3: "温湿度检测仪",
          k4: "22:52:50",
        },
        {
          k1: "2",
          k2: "电力舱",
          k3: "温湿度检测仪",
          k4: "22:52:50",
        },
        {
          k1: "3",
          k2: "电力舱",
          k3: "温湿度检测仪",
          k4: "22:52:50",
        },
        {
          k1: "4",
          k2: "电力舱",
          k3: "温湿度检测仪",
          k4: "22:52:50",
        },
      ]),
    },
    tip3: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 1,
        },
        {
          label: "员工姓名",
          prop: "k2",
          width: 2,
        },
        {
          label: "工单数量",
          prop: "k3",
          width: 1,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "王小五",
          k3: "5",
        },
        {
          k1: "2",
          k2: "刘小三",
          k3: "5",
        },
      ]),
    },
    tip4: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 1.2,
        },
        {
          label: "设备类型",
          prop: "k2",
          width: 2,
        },
        {
          label: "采集数据条数",
          prop: "k3",
          width: 2,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "温湿度检测仪",
          k3: "20",
        },
        {
          k1: "2",
          k2: "温湿度检测仪",
          k3: "30",
        },
        {
          k1: "3",
          k2: "温湿度检测仪",
          k3: "26",
        },
      ]),
    },
  },
});

const tips = pageRefData.value.tips;
const handlers = {
  h1: (item) => {
    tips.tip1.tableData = item.detail.map((item, index) => ({
      k1: index + 1,
      k2: item["name"],
      k3: item["number"],
    }));
    tips.tip1.visible = true;
  },
  // h2: () => {
  //   pageRefData.value.tips.tip2.visible = true;
  // },
  // h3: () => {
  //   pageRefData.value.tips.tip3.visible = true;
  // },
  h4: (item) => {
    const tip4 = pageRefData.value.tips.tip4;
    tip4.tableData = markRaw(item.list);
    tip4.visible = true;
  },
};

const options = pageRefData.value.options;
const apisMap = new Map([
  [
    "getDeviceAreaAnalysis",
    {
      api_pro: getDeviceAreaAnalysis,
      params: {},
      callback(res) {
        const colorList = [
          "rgba(244, 217, 130, 1)",
          "rgba(232, 141, 107, 1)",
          "rgba(94, 137, 238, 1)",
          "rgba(97, 226, 157, 1)",
          "rgba(230, 126, 34,1.0)",
          "rgba(26, 188, 156,1.0)",
        ];
        options.option4 = markRaw(createChartOp4(res.handledData, colorList));
        pageRefData.value.section3 = markRaw(
          res.handledData.map((item, index) => ({
            label: item.name,
            icon_color: colorList[index],
            val: ((item.value * 100) / res.total).toFixed(2) + "%",
          }))
        );
      },
    },
  ],
  [
    "getDeviceOperationAnalysis",
    {
      api_pro: getDeviceOperationAnalysis,
      params: {
        day: "7",
      },
      callback(res) {
        options.option7 = markRaw(createChartOp7(res));
      },
    },
  ],
  [
    "getWorkOrderAnalysis",
    {
      api_pro: getWorkOrderAnalysis,
      params: {
        day: "7",
      },
      callback(res) {
        options.option5 = markRaw(createChartOp5(res));
      },
    },
  ],
  [
    "getPersonnelOperationAnalysis",
    {
      api_pro: getPersonnelOperationAnalysis,
      callback(handledData) {
        console.log(handledData, "人员操作分析处理后数据");

        pageRefData.value.section1 = markRaw(handledData);
      },
    },
  ],
  [
    "entranceApplybehaviorAnalyse",
    {
      api_pro: entranceApplybehaviorAnalyse,
      params: {
        day: "7",
      },
      callback(handledData) {
        console.log("入廊行为分析----》", handledData);
        options.option6 = markRaw(createChartOp6(handledData));
      },
    },
  ],
  [
    "deviceAccessAnalyse",
    {
      api_pro: deviceAccessAnalyse,
      params: {
        day: "7",
      },
      callback(handledData) {
        const arr = pageRefData.value.section2.viewData.map((item) => {
          const infoItem = handledData.get(item.name);

          if (infoItem) {
            return {
              ...item,
              val: infoItem.value,
              list: infoItem.list,
            };
          }

          return {
            ...item,
          };
        });
        console.log("设备数据接入分析----》", handledData, arr);
        pageRefData.value.section2.viewData = markRaw(arr);
      },
    },
  ],
  // [
  //   "environmentAnalysisData",
  //   {
  //     api_pro: environmentAnalysisData,
  //     params: {
  //       startDate:null,
  //       endTime:null,
  //       partitionCode:'tonghenanlu'
  //     },
  //     callback(res) {
  //       console.log(res, '环境数据１');
  //       options.option1 = res['甲烷气体检测仪']
  //       options.option2 = res['硫化氢气体检测仪']
  //     },
  //   },
  // ],
]);
// const getData = () => {
//   options.option1 = markRaw(createChartOp1());
//   options.option2 = markRaw(createChartOp1());
//   options.option3 = markRaw(createChartOp1());
//   options.option4 = markRaw(createChartOp4());
//   options.option5 = markRaw(createChartOp5());
//   options.option6 = markRaw(createChartOp6());
//   options.option7 = markRaw(createChartOp7());
//   patchReq([...apisMap.values()]); // 批量请求数据
// };
const store = useStore()
const currentKey = reactive({
  currentKey3: "7",
  currentKey6: "7",
  currentKey7: "7",
  currentKey1: "7",
});
const getEnv = (flag) => {
    const [code = "zhc", num = 1] = store.state.pageTwoCode;
    console.log(code, num);
    flag && (loading3.value = true)
    environmentAnalysisData({
      partitionCode: "tonghenanlu_fenqu" + num + "_" + code,
    }).then(setOption).finally(()=>{
      loading3.value = false
    })
  }
watch(
  () => store.state.pageTwoCode,
  getEnv,
  {
    immediate: true,
  }
);
const getData = async (type) => {
  // 设置默认图表配置
  // options.option1 = markRaw(createChartOp1());
  // options.option2 = markRaw(createChartOp1());
  // options.option3 = markRaw(createChartOp1());
  options.option4 = markRaw(createChartOp4());
  options.option5 = markRaw(createChartOp5());
  options.option6 = markRaw(createChartOp6());
  options.option7 = markRaw(createChartOp7());
  const obj = {
    l1: { loading: loading1, keys: ["getPersonnelOperationAnalysis"] },
    l2: { loading: loading2, keys: ["getDeviceAreaAnalysis"] },
    // l3: {
    //   loading: loading3,
    //   keys: ["environmentAnalysisData"],
    // },
    l4: { loading: loading4, keys: ["deviceAccessAnalyse"] },
    l5: { loading: loading5, keys: ["getWorkOrderAnalysis"] },
    l6: { loading: loading6, keys: ["entranceApplybehaviorAnalyse"] },
    l7: { loading: loading7, keys: ["getDeviceOperationAnalysis"] },
  };

  const item = obj[type];
  if (item?.loading) item.loading.value = true;
  if (!type) {
    await patchReq([...apisMap.values()]);
  } else {
    const apis = (item.keys || [])
      .map((key) => {
        console.log(key, mapKey[key]);
        const item = apisMap.get(key);
        if (item.params) {
          item.params.day = currentKey[mapKey[key]];
        }
        return item;
      })
      .filter(Boolean);
    const res = await patchReq(apis);
    msg.value = res.every(i=>i.code =='00000') ? '请求成功':'请求失败'
  }

  // 结束 loading 状态
  if (item?.loading) item.loading.value = false;
};
const mapKey = {
  getWorkOrderAnalysis: "currentKey3",
  entranceApplybehaviorAnalyse: "currentKey6",
  getDeviceOperationAnalysis: "currentKey7",
  deviceAccessAnalyse: "currentKey1",
};
// 页面switch切换
const handleSwitchChange = async (item, apiName) => {
  const key = mapKey[apiName];
  if (key) {
    currentKey[key] = item.key;
  }
  const apiConfig = apisMap.get(apiName);
  if (apiConfig) {
    const res = await apiConfig.api_pro({
      day: item.key,
    });
    apiConfig.callback(res);
  }
};
const task1 = () => getData('l1')
const task2 = () => getData('l2')
const task4 = () => getData('l4')
const task5 = () => getData('l5')
const task6 = () => getData('l6')
const task7 = () => getData('l7')
const task8 = () => getEnv(true)
onMounted(() => {
  getData();
  [task1,task2,task4,task5,task6,task7,task8].forEach(pollingManager.register)
});
onUnmounted(()=>{
  [task1,task2,task4,task5,task6,task8,task7].forEach(pollingManager.unregister)
})
</script>

<style lang="less" scoped>
.bg-radius-gradient {
  background-image: radial-gradient(
    circle,
    #30303059 0%,
    #30303059 60%,
    #ffffff00 60%
  );
}

:deep(.c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
    background-image: initial;
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    background-image: initial;
  }
}
</style>
