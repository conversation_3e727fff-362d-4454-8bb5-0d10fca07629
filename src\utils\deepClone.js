export default function deepClone(obj, visited = new WeakMap()) {
    if (typeof obj !== "object" || obj === null) return obj;
    // 防止循环引用
    if (visited.has(obj)) {
      return visited.get(obj);
    }
    // 获取对象的构造函数
    const constructor = obj.constructor;
    // 处理特殊对象类型~正则对象和时间对象
    if(/^(RegExp|Date|WeakMap|WeakSet)/.test(constructor.name)) {
        const res = new constructor(obj);
        visited.set(obj, res);
        return res;
    }
    // Map
    if (constructor === Map) {
      const map = new Map();
      visited.set(obj, map);
      obj.forEach((value, key) => {
        map.set(key, deepClone(value, visited));
      });
      return map;
    }
    // Set
    if (constructor === Set) {
      const set = new Set();
      visited.set(obj, set);
      obj.forEach((value) => {
        set.add(deepClone(value, visited));
      });
      return set;
    }
    // 处理函数对象和箭头函数
    if(constructor === Function) {
        let res;
        if(!obj.hasOwnProperty("prototype")) { // 箭头函数
            res = new Function(`return ${obj.toString()}`)()
            visited.set(obj, res); // 避免循环引用
            return res;
        }
        // 考虑到构造函数和普通对象
        res = function(...args) {
            return obj.call(this, ...args);
        }
        visited.set(obj, res); // 避免循环引用
        // 普通对象的自身的属性，比如fn.a = 2这种静态属性
        Object.keys(obj).forEach(key => res[key] = obj[key]);
        // 原型继承，寄生组合继承，复制该函数的原型链
        res.prototype = Object.create(obj.prototype);
        res.prototype.constructor = res;
        return res;
    }
    // 处理普通对象和数组
    const result = Array.isArray(obj) ? [] : {};
    visited.set(obj, result);
    // 获取对象的所有属性名，包括不可枚举属性
    const props = Object.getOwnPropertyNames(obj); // 不可枚举类型
    const symbolProps = Object.getOwnPropertySymbols(obj); // Symbol类型
    props.concat(symbolProps).forEach(key => {
        const descriptor = Object.getOwnPropertyDescriptor(obj, key);
        if(descriptor) {
            const { value, writable, enumerable, configurable } = descriptor;
            Object.defineProperty(result, key, {
                value: deepClone(value, visited),
                writable, enumerable, configurable
            })
        }
    });
    return result;
}