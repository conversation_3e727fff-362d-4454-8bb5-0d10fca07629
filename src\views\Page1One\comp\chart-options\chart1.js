import * as echarts from "echarts";
import dayjs from "dayjs";
export const createChartOp1 = (data) => {
  let rawData = []
  if (!data || (Array.isArray(data) && data.length == 0)) {
    const today = dayjs();
    const last7Days = Array.from({ length: 7 }).map((_, i) => {
      return today.subtract(6 - i, 'day').format('MM-DD'); // 从6天前到今天
    });
    rawData = last7Days.map(item=>({name:item,value: 0}))
  }else{
    rawData = data
  }
  const dataset = {
    dimensions: ["name", "value"],
    source: rawData,
  };
  const option = {
    grid: {
      right: 0,
      bottom: 0,
      left: 0,
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      padding: 22,
      formatter: function (params) {
        console.log(params);
        const item = params[0]; // 取第一个系列的数据项
        return `${item.name}<br/>数量: ${item.value?.value} 次`;
      },
      backgroundColor: "rgba(27, 23, 21, 0.80)",
      textStyle: {
        fontSize: 40,
        color: 'fff',
      },
    },

    xAxis: {
      type: "category",
      // data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
    },
    yAxis: {
      type: "value",
      name: "次",
      nameTextStyle: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
        align: "center",
        padding: [0, 50, 10, 0],
      },
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
      axisLine: {
        itemStyle: {
          color: "rgba(203, 221, 242, 0.30)",
        },
      },
    },
    dataset,
    series: [
      {
        // data: [120, 200, 150, 80, 70, 110, 130],
        type: "bar",
        barWidth: "20px",
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(254, 225, 134, 1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(105, 193, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  };
  return option;
};

export const createChartOp2 = (data, colors) => {
  const rawData = data ?? [
    {
      name: "a",
      value: 2,
    },
    {
      name: "b",
      value: 1.2,
    },
    {
      name: "c",
      value: 2.4,
    },
    {
      name: "d",
      value: 2.6,
    },
  ];
  const maxValue = rawData.reduce((pre,cur)=>pre+=cur.value,0);  
  const dataset = {
    dimensions: ["name", "value"],
    source: rawData,
  };
  const option = {
    dataset,
    polar: {
      radius: ["20%", "80%"],
      center: ["50%", "50%"],
    },
    angleAxis: {
      max: maxValue,
      startAngle: 90,
      clockwise: false,
      splitNumber: 6,
      axisLine: {
        show: true,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: true,
      },
    },
    radiusAxis: {
      type: "category",
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        type: "bar",
        coordinateSystem: "polar",
        showBackground: true, // 展示背景阴影
        roundCap: true,
        barWidth: 6,
        colorBy: "data",
        color: colors,
      },
    ],
  };
  return option;
};

export const createChartOp3 = (arr) => {
  // 在option1的基础上修改
  const option = createChartOp1(arr);
  option.yAxis.name = "kWh";
  // option.tooltip.formatter=function (params) {
  //   const item = params[0]; // 取第一个系列的数据项
  //   return `${item.name}<br/>能耗: ${item.value?.value} kWh`;
  // }
  option.series[0].itemStyle.color.colorStops[0].color = "rgba(0, 255, 255, 1)";
  option.series[0].itemStyle.color.colorStops[1].color =
    "rgba(105, 193, 255, 0)";
  return option;
};
