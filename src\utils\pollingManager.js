const tasks = new Set()
let timer = null

function startPolling(interval = 60000) {
  if (timer) return
  timer = setInterval(() => {
    tasks.forEach(fn => fn())
  }, interval)
}

function stopPolling() {
  clearInterval(timer)
  timer = null
}

function register(fn) {
  tasks.add(fn)
  startPolling()
}

function unregister(fn) {
  tasks.delete(fn)
  if (tasks.size === 0) stopPolling()
}

export default { register, unregister }
