import dayjs from "dayjs";
import request from "../request";

// 报警消息未读列表
export async function getInspectionData(params) {
  const res = await request(
    "/admin/pipe/monitor/alarm/queryIsReadAlarm",
    params
  );
  const list = res.data?.records ? res.data.records : res.data
  const handleData = list.map((item) => {
    return {
      id: item.id,
      warningMsg: item.equipmentName + "发生" + item.alarmDescription,
      warningTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
      // type: item.source == 1 ? "type1" : "type2", // type1 设备报警、type2 其他报警
      type: item.alarmClassify,
      detail: item,
      equipmentCode:item.equipmentCode,
      alarmDescription: item.alarmDescription
    };
  });
  const total = res?.data?.total ?? 0
  handleData.total = total
  return handleData;
}

// 未读更改为已读
export async function updateIsReadAlarm(params) {
  const res = await request(
    "/admin/pipe/monitor/alarm/updateIsReadAlarm",
    params,
    "POST"
  );

  return res;
}
