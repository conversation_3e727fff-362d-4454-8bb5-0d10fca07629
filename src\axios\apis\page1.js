import request from "../request";
import { setToken } from "@/auth";

import dayjs from "dayjs";
import { standardization } from "@/utils/num";

// **运行态势**

// 总体概览
export async function totalOverview(params) {
  const res = await request(
    "/admin/pipe/inspection/business/pipelineInfo/totalOverview"
  );
  const data = res.data;
  const handledData = {
    k1: data.invest,
    k2: data.deviceNum,
    k3: data.pipeGalleryLength,
    k4: data.pipelineLength ?? "0"
  };
  handledData.code = res.code
  return handledData;
}

// 运行态势-设备状态分析
export async function getDeviceStatus(params) {
  const res = await request(
    "/admin/pipe/bigScreen/device/analyse/deviceStatus",
    params,
  );
  const data = res.data;
  const handledData = data.list.map((item) => {
    if (item.sumCount < 1) {
      return {
        label: item.name,
        t1: 0,
        t1p: 0 + "%",
        t2: 0,
        t2p: 0 + "%",
        total: item.sumCount,
      };
    }
    const transformedItem = {
      label: item.name,
      t1: item.noralCount,
      t1p: Math.round((item.noralCount * 100) / item.sumCount) + "%",
      t2: item.warnCount,
      t2p: Math.round((item.warnCount * 100) / item.sumCount) + "%",
      total: item.sumCount,
    };

    return transformedItem;
  });

  return { list: handledData, total: data.sumCount, code: res.code };
}
export async function AlarmgetdailyAlarmListInfo(params) {
  const res = await request(
    "/admin/pipe/monitor/alarm/panoramicViewOfPipeGallery/AlarmgetdailyAlarmListInfo",
    params,
    "POST"
  );
  const obj = res.data || {};
  const chartData = Object.entries(obj).map(([dateStr, count]
  ) => ({
    name: dayjs(dateStr).format("MM-DD"),
    value
      : count
  }));
  return {
    chartData,
    code: res.code
  };
}
export async function getVideoLive(params) {
  return request('/admin/video/device/getDeviceLive', params)
}
export async function getDailyAlarmStatistics(params) {
  const res = await request(
    "admin/pipe/monitor/alarm/panoramicViewOfPipeGallery/dailyAlarmStatistics",
    params,
    "POST"
  );
  // res.data.push(
  //   ...[
  //     {
  //       road: null,
  //       roadCode: null,
  //       cabin: null,
  //       cabinCode: null,
  //       partitionArea: null,
  //       partitionAreaCode: null,
  //       clas: "环控系统", // 系统名称
  //       alarmTime: null,
  //       number: 1, //告警数量
  //     },
  //     {
  //       road: null,
  //       roadCode: null,
  //       cabin: null,
  //       cabinCode: null,
  //       partitionArea: null,
  //       partitionAreaCode: null,
  //       clas: "安防系统", // 系统名称
  //       alarmTime: null,
  //       number: 1, //告警数量
  //     },
  //     {
  //       road: null,
  //       roadCode: null,
  //       cabin: null,
  //       cabinCode: null,
  //       partitionArea: null,
  //       partitionAreaCode: null,
  //       clas: "通信系统", // 系统名称
  //       alarmTime: null,
  //       number: 1, //告警数量
  //     },
  //     {
  //       road: null,
  //       roadCode: null,
  //       cabin: null,
  //       cabinCode: null,
  //       partitionArea: null,
  //       partitionAreaCode: null,
  //       clas: "通信系统", // 系统名称
  //       alarmTime: null,
  //       number: 5, //告警数量
  //     },
  //   ]
  // );

  const cateValues = new Map([
    ["安防系统", 0],
    ["通信系统", 0],
    ["消防系统", 0],
    ["环控系统", 0],
  ]);

  res.data.forEach((item) => {
    const cate = item.clas;
    cateValues.set(cate, cateValues.get(cate) + item.number);
  });
  const total = [...cateValues.values()].reduce((acc, cur) => acc + cur, 0);
  // const total = 0
  const colors = [
    "rgba(244, 217, 130, 1)",
    "rgba(232, 141, 107, 1)",
    "rgba(94, 137, 238, 1)",
    "rgba(97, 226, 157, 1)",
  ];
  const len = colors.length;
  const chartData = [];
  const legends = [];
  [...cateValues.entries()].forEach((item, index) => {
    chartData.push({
      name: item[0],
      value: item[1],
      itemStyle: {
        color: colors[index % len],
      },
    });
    legends.push({
      label: item[0],
      icon_color: colors[index % len],
      val: (total == 0 ? 0 : standardization((item[1] * 100) / total)) + "%",
    });
  });
  return {
    chartData,
    colors,
    legends,
    code: res.code
  };
}

// 告警详情列表
export async function findPartitionAlarmInfo(params) {
  const res = await request(
    "/admin/pipe/monitor/partitionoMnitoring/findPartitionAlarmInfo",
    params,
    "POST"
  );
  const data = res.data;

  const handledData = data.map((item, index) => ({
    k1: index + 1,
    k2: item.equipmentName,
    k3: item.road,
    k4: item.alarmDescription,
    k5: dayjs(item.alarmTime).format("YYYY-MM-DD HH:mm:ss"),
    k6: item.alarmLevel,
    detail: item,
  }));
  return handledData;
}

// 工单处理分析
export async function getWorkOrderAnalysis(params) {
  const res = await request(
    "/admin/pipe/bigScreen/personnel/analyse/orderAnalyse",
    params
  );
  const keyLabelMap = new Map([
    ["daichuli", "k2"],
    ["chaoshidaichuli", "k4"],
    ["chaoshiyichuli", "k3"],
    ["yichuli", "k1"],
  ]);
  let total = 0;
  const categoryData = {
    k1: 0,
    k1_list: [],
    k2: 0,
    k2_list: [],
    k3: 0,
    k3_list: [],
    k4: 0,
    k4_list: [],
  };

  for (const cur of res.data) {
    for (const key of keyLabelMap.keys()) {
      const ck = keyLabelMap.get(key);
      const val = cur[key] || 0;
      const list = cur[key + "List"] || [];

      categoryData[ck] += val;
      categoryData[ck + "_list"].push(...list);
      total += val;
    }
  }
  const handledData = {};
  for (let key in categoryData) {
    if (key.endsWith("_list")) {
      handledData[key] = categoryData[key];
    } else {
      const num = Math.round((categoryData[key] * 100) / total)
      handledData[key] = {
        val: categoryData[key],
        percent: isNaN(num) ? "0%" : num + '%',
      };
    }
  }
  handledData.code = res.code
  return { handledData, res }
}

// 当日值班详情
export async function inquireThePersonOnDutyThatDay(params) {
  const res = await request(
    "admin/pipe/monitor/alarm/panoramicViewOfPipeGallery/inquireThePersonOnDutyThatDay",
    params,
    "POST"
  );
  let currentName = ""; // 当前值班人员
  let currentPhone = ''
  const handledData = res.data.map((item) => {
    if (!currentName && item.isNow === "1") {
      currentName = item.userName;
      currentPhone = item.phone
    }
    return {
      k1: item.userName,
      k2: item.groupType,
      k3: item.bc,
      k4: item.phone,
    };
  });
  return { currentName, currentPhone, handledData, code: res.code };
}

// 视频监控 - 有问题
export async function getVideoMonitorData(params) {
  const res = await request(
    "/admin/pipe/monitor/alarm/panoramicViewOfPipeGallery/findVideos",
    params,
    "POST"
  );
  return res;
}

// 获取节点树
export async function getTreeData() {
  // 在router-view之外，这里实在router.beforeEach之前执行，无法确认已经获取了apiToken,先确认token是否存在，所有一共可能会请求三次token
  // if (!window._apiToken) await setToken();
  const res = await request("/admin/obj/generalObjectBind/tree");
  const handledData = res.data;
  return handledData;
}

// 获取节点树选择数据
export async function getTreeSelectData(params) {
  const data = await getTreeData();

  const parDataToOptions = (data) => {
    // debugger;
    const arr = data?.children || [];
    const option = arr.map((item, index) => {
      const optionItem = {
        value: item.code,
        label: item.name,
        children: parDataToOptions(item),
      };
      if (!optionItem.children.length)
        Reflect.deleteProperty(optionItem, "children");
      return optionItem;
    });
    return option;
  };
  const options = parDataToOptions(data[0]);
  return options;
}

// 获取设备数据 按类型、名称查询
const obj = {
  NORMAL: '正常',
  WARN: '告警',
  OFFLINE: '离线',
  FAULT: '故障',
  DISABLE: '禁用'
}
export async function getDevicesData(params = {}) {
  let res = await request("/admin/device/status/pagination", params, "POST");
  if (!res.data) {
    res.data = {
      "records": [
      ],
      "total": 0,
      "size": params.size || 10,
      "current": params.current || 1,
      "orders": [],
      "searchCount": true,
      "pages": 6
    }

  }

  const formatDate = (date) => {
    if (!date) return "";
    // 2024年6月27日
    return dayjs(date).format("YYYY年MM月DD日 ");
  };
  const { current, size } = res.data
  const tableData = res.data.records.map((item, index) => {
    return ({
      k1: (current - 1) * size + index + 1,
      k2: item.deviceCode,
      k3: item.deviceTypeName,
      k4: item.deviceName,
      k5: obj[item.deviceStatus],
      k6: "定位",
      deviceTypeId: item.deviceTypeId,
      deviceId: item.deviceId
    })
  });

  const pageInfo = {
    pages: res.data.pages,
    size: res.data.size,
    total: res.data.total,
    current: res.data.current,
  };

  const handledData = {
    tableData,
    pageInfo,
  };

  return handledData;
}

// 获取设备数据 按位置查询
export async function getDeviceData2(params = {}) {
  const { current = 1, size = 10 } = params;
  const res = await request("/admin/pipe/monitor/equipmentMonitoring/exampleQueryDeviceInformationAboutAStreetOrZone", params, 'POST');
  const data = res?.data || [];
  let arr = data.flat(10)
  const start = (current - 1) * size
  let d = arr.slice(start, start + size)
  const formatDate = (date) => {
    if (!date) return "";
    // 2024年6月27日
    return dayjs(date).format("YYYY年MM月DD日");
  };
  const tableData = d.map((item, index) => {

    return ({
      k1: start + index + 1,
      k2: item.code,
      k3: item.deviceTypeName,
      k4: item.deviceTypeCode,
      k5: (item.name + "").split("#").at(-1),
      k6: obj[item.deviceStatus],
      k7: "定位",
      equipmentCode: item.equipmentCode
    })
  })

  const pageInfo = {
    pages: Math.ceil(arr.length / size),
    size,
    total: arr.length,
    current,
  };

  const handledData = {
    tableData,
    pageInfo,
  };

  return handledData;
}

// 获取设备类型映射关系
export async function getDeviceTypeLabelMap(params) {
  const res = await request("/admin/device/type/list", params);

  const data = res?.data || [];

  const options = data.map((item) => ({
    value: item.code,
    label: item.name,
    id: item.id
  }));
  localStorage.setItem('options', JSON.stringify(options))
  return options;
}

// 实时事件
export async function getRealTimeEvents(params) {
  const _params = {
    current: 1,
    query: {
      cabinId: "",
      beginTime: "",
      endTime: "",
    },
    size: 10,
    ...params,
  };
  const res = await request(
    "/admin/pipe/monitor/alarm/findZhjkAlarmInfo",
    _params,
    "POST"
  );

  const data = res?.data?.records || [];
  const handledData = data.map((item, index) => {
    return {
      title: item.alarmDescription,
      des: item.road + " " + item.alarmDescription,
      time: dayjs(item.alarmTime).format("YYYY-MM-DD HH:mm:ss"),
    };
  });
  handledData.code = res.code
  return handledData;
}
// 获取设备运行历史数据
export async function getDeviceRunHistoryData(params = {}) {
  const {
    i_current = 1,
    i_size = 10,
    deviceCode = "1Z14ETH01",
    deviceStatuses = ["NORMAL", "OFFLINE", "FAULT", "WARN", "DISABLE"],
    statusTimeFrom = "",
    statusTimeTo = "",
  } = params;

  const deviceIds = [`hk_provider${deviceCode}`];

  const payload = {
    current: i_current,
    size: i_size,
    query: {
      deviceIds: deviceIds,
      deviceStatuses,
      statusTimeFrom,
      statusTimeTo,
    },
  };
  const res = await request("/admin/device/status/pagination", payload, "POST");

  const data = res?.data || {};
  const { total, size, current } = data;

  const lines = res?.data?.records || [];

  const pageInfo = {
    pages: Math.ceil(total / size),
    size: size,
    total: total,
    current: current,
  };

  const tableData = lines.map((item, index) => {
    const msgObj = item?.warnMessages.at(0) || {};
    const msg = msgObj?.message || "";
    const code = msgObj?.code || "";
    const describe = code + msg;
    const continueTimeStr = item.continueTimeStr || "";

    return {
      k1: index + 1,
      k2: item.createTime,
      k3: msg,
      k4: describe,
      k5: continueTimeStr,
      k6: "定位",
      deviceCode: item.deviceCode,
    };
  });

  return { tableData, pageInfo };
}
export async function getNhAnalysis() {
  return request('/admin/pipe/bigScreen/circumstances/analyse/nhAnalysis')

}