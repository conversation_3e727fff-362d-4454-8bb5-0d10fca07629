<template>
  <div class="flex gap-x-[32px] flex-evenly flex-col mt-[30px]">
    <!-- <PanelTitle v-loading="loading" title="告警统计分析" class="mt-[88px] relative w-full">  </PanelTitle> -->
    <div class="bg-position-[0%_0%] bg-repeat bg-[url(@/assets/imgs/page1/p59.png)] w-full h-[76px]"></div>
    <div
      class="h-[500px] w-[100%] px-[56px] box-border pt-[12px] flex gap-[20px]"
    >
      <!-- 告警统计分析 -->
      <div class="flex-1">
        <PanelTitle v-loading="loading1" :element-loading-text="msg">
          <Refresh @click="sevenList" class="right-[100px] top-[-50px]" />
          <div
            class="h-[72px] cus-bg-full bg-[url('@/assets/imgs/page1/p50.png')]"
          ></div>
          <div class="h-[390px]">
            <Echart :option="pageRefData.option1" />
          </div>
        </PanelTitle>
      </div>
      <!-- 当日告警数量统计 -->
      <div class="flex-1">
        <PanelTitle v-loading="loading2" :element-loading-text="msg">
          <Refresh
            @click="getData('right')"
            class="right-[100px] top-[-60px]"
          />

          <div
            class="h-[72px] cus-bg-full bg-[url('@/assets/imgs/page1/p48.png')]"
          ></div>
          <div class="h-[370px] flex items-center">
            <div
              class="w-[314px] h-[314px] rounded-full bg-radius-gradient ml-[32px]"
            >
              <Echart :option="pageRefData.option2" />
            </div>
            <div class="flex flex-col gap-y-[12px] ml-auto">
              <div
                v-for="item in pageRefData.section3"
                :key="item.label"
                class="flex items-center w-[394px] h-[60px] bg-[url('@/assets/imgs/page1/p60.png')] cus-bg-full"
              >
                <div
                  class="w-[14px] h-[14px] rounded-full ml-[26px]"
                  :style="{ backgroundColor: item.icon_color }"
                ></div>
                <div class="text-[32px] text-[#CBDDF2] ml-[16px]">
                  {{ item.label }}
                </div>
                <div
                  class="text-[36px] text-[#CBDDF2] ml-auto font-medium pr-[20px]"
                >
                  {{ item.val }}
                </div>
              </div>
            </div>
          </div>
        </PanelTitle>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, markRaw, onMounted, watch, onUnmounted } from "vue";
import {
  getDailyAlarmStatistics,
  AlarmgetdailyAlarmListInfo,
  getRealTimeEvents,
} from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";
import { createChartOp1, createChartOp2 } from "../chart-options/chart1";
import Refresh from "@/components/my-ui/Refresh.vue";
import PanelTitle from "@/components/my-ui/PanelTitle.vue";
import pollingManager from '@/utils/pollingManager'
import { useStore } from "vuex";
import { getSevenDayList } from "@/axios/apis/page2";
const store = useStore();
const loading1 = ref(false);
const loading2 = ref(false);
const props = defineProps({
  flag: String,
});
const pageRefData = ref({
  option1: {},
  option2: {},
  section3: [
    {
      label: "安防",
      icon_color: "rgba(97, 226, 157, 1)",
      val: "20%",
    },
    {
      label: "通信",
      icon_color: "rgba(244, 217, 130, 1)",
      val: "12%",
    },
    {
      label: "消防",
      icon_color: "rgba(232, 141, 107, 1)",
      val: "27%",
    },
    {
      label: "环控",
      icon_color: "rgba(94, 137, 238, 1)",
      val: "17%",
    },
  ],
  section5: [
    {
      title: "入廊提醒",
      time: "2024-04-17 16:00",
      des: "危险气体浓度超出安全标准，请立即采取应浓度超出安全",
    },
  ],
});

const apisMapLeft = new Map([
  [
    // 近七天告警统计分析
    "AlarmgetdailyAlarmListInfo",
    {
      api_pro: AlarmgetdailyAlarmListInfo,
      params: {
        // code: "tonghenanlu_fenqu" + store.state.pageTwoCode[1] + '_' + store.state.pageTwoCode[0],
      },
      callback(res) {
        const { chartData } = res;
        pageRefData.value.option1 = markRaw(createChartOp1(chartData));              
      },
    },
  ],
]);
 const sevenList = async () => {
    const [code = "zhc", num = 1] = store.state.pageTwoCode;
    try {
      loading1.value = true;
      const res = await AlarmgetdailyAlarmListInfo({
        code: `tonghenanlu_fenqu`,
        //${num}_${code}
      });
      const { chartData } = res;
      msg.value= res,code == '00000' ? '请求成功':'请求失败'
      pageRefData.value.option1 = markRaw(createChartOp1(chartData));
    } catch (error) {
      msg.value = '请求失败'
    } finally {
      loading1.value = false;
    }
  }
// watch(
//   () => store.state.pageTwoCode,
// sevenList
// );
const apisMapRight = new Map([
  // 实时事件
  // [
  //   "getRealTimeEvents",
  //   {
  //     api_pro: getRealTimeEvents,
  //     params: {
  //       current: 1,
  //       query: {
  //         code: "tonghenanlu_fenqu1_zhc",
  //         cabinId: "",
  //         beginTime: "2025-5-16",
  //         endTime: "2025-5-16",
  //       },
  //       size: 10,
  //     },
  //     callback(handledData) {
  //       pageRefData.value.section5 = markRaw(handledData);
  //     },
  //   },
  // ],
  [
    // 当日告警统计分析
    "getDailyAlarmStatistics",
    {
      api_pro: getDailyAlarmStatistics,
      params: {},
      callback(res) {
        const { chartData, colors, legends } = res;
        pageRefData.value.option2 = markRaw(createChartOp2(chartData, colors));        
        pageRefData.value.section3 = markRaw(legends);
      },
    },
  ],
]);
const msg = ref("");
const getData = async (type) => {
  pageRefData.value.option1 = markRaw(createChartOp1());
  pageRefData.value.option2 = markRaw(createChartOp2());
  const typeMap = {
    left: { apis: apisMapLeft, loading: loading1 },
    right: { apis: apisMapRight, loading: loading2 },
  };
  if (!type) {
    patchReq([...apisMapLeft.values()]);
    patchReq([...apisMapRight.values()]);
    return;
  }
  const { apis, loading } = typeMap[type] || {};
  if (!apis || !loading) return;

  loading.value = true;
  const res = await patchReq([...apis.values()]);
  msg.value = res.every((i) => i.code === "00000") ? "请求成功" : "请求失败";
  loading.value = false;
};
let taskRef = null
onMounted(() => {
  getData();
  taskRef = ()=>getData('right')
  pollingManager.register(taskRef)
  pollingManager.register(sevenList)
});
onUnmounted(() => {
  pollingManager.unregister(taskRef)
  pollingManager.unregister(sevenList)
})
</script>

<style lang="less" scoped>
</style>
