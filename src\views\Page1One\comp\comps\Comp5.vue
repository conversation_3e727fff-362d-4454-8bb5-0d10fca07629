<template>
  <div>
    <PanelTitle v-loading="loading" :element-loading-text="msg">
      <div class="bg w-full h-[76px]"></div>
      <div class="h-[628px] pt-[46px] px-[56px] box-border">
        <MySwitch
          :options="pageStaticData.section1.ops"
          val="7"
          class="justify-end"
          @switchChange="
            (item) => handleSwitchChange(item, 'getWorkOrderAnalysis')
          "
        ></MySwitch>
        <div
          id="order-container"
          class="flex items-center justify-around mt-[44px]"
        >
          <!-- <div
            v-for="item in viewData"
            :key="item.title"
            class="flex flex-col items-center cus-use-click"
            @click="handles.section1ClickHandle(item)"
          >
            <div
              class="w-[348px] h-[219px] cus-bg-full bg-[url('@/assets/imgs/page1/p11.png')] flex cus-flex-center relative"
            >
              <div
                class="cus-bg-full absolute top-0"
                :style="{
                  width: item.icon_style.w,
                  height: item.icon_style.h,
                }"
                :class="[item.icon_class]"
              ></div>
            </div>
            <div
              class="text-center my-[22px] text-[42px]"
              :style="{ color: item.color }"
            >
              {{ item.title }}
            </div>
            <div
              class="w-[330px] h-[96px] cus-bg-full bg-[url('@/assets/imgs/page1/p58.png')] flex justify-center items-center whitespace-nowrap"
            >
              <span class="text-[40px] text-[#CBDDF2FF]"
                ><span
                  class="text-[40px] text-[#CBDDF2FF] font-medium mr-[8px]"
                  >{{ item.val }}</span
                >个</span
              >
              <div class="w-[2px] h-[30px] bg-[#CBDDF2FF] mx-[10px]"></div>
              <span>{{ item.percent }}</span>
            </div>
          </div> -->
        </div>
      </div>
      <Refresh @click="getOrder(true)" class="right-[100px] top-[40px]" />
    </PanelTitle>
    <!-- 工单分析 -->
    <My-Tip
      stl="right-[1920px] top-[800px] ct-1"
      v-model:visible="pageRefData.tip.visible"
    >
      <div class="w-[546px] h-[408px]">
        <C-Table
          :titles="pageRefData.tip.titles"
          :table-data="pageRefData.tip.tableData"
          max-height="300px"
        ></C-Table>
      </div>
    </My-Tip>
  </div>
</template>

<script setup>
import { ref, markRaw, onMounted, onUnmounted } from "vue";
import { getWorkOrderAnalysis } from "@/axios/apis/page1";
import patchReq from "@/utils/patchReq";
import Refresh from "@/components/my-ui/Refresh.vue";
import { getImg,getPage1Img } from "@/utils/assets";
import pollingManager from '@/utils/pollingManager'
const props = defineProps({
  flag: String,
});
const loading = ref(false);
const pageStaticData = {
  section1: {
    ops: [
      {
        label: "近七天",
        key: "7",
      },
      {
        label: "近30天",
        key: "30",
      },
      {
        label: "近半年",
        key: "180",
      },
    ],
  },
};
const viewData = ref([
  {
    title: "已完成",
    icon_class: "p88.png",
    icon_style: {
      w: "111px",
      h: "120px",
    },
    val: "0",
    percent: "0%",
    color: "#65EAA2FF",
    prop: "k1",
    list: [],
  },
  {
    title: "待处理",
    icon_class: "p89.png",
    icon_style: {
      w: "126px",
      h: "120px",
    },
    val: "0",
    percent: "0%",
    color: "#FFF278FF",
    prop: "k2",
    list: [],
  },
  {
    title: "超时完成",
    icon_class: "p6.png",
    icon_style: {
      w: "108px",
      h: "108px",
    },
    val: "0",
    percent: "0%",
    color: "#748DEDFF",
    prop: "k3",
    list: [],
  },
  {
    title: "超时未处理",
    icon_class: "p90.png",
    icon_style: {
      w: "117px",
      h: "120px",
    },
    val: "0",
    percent: "0%",
    color: "#E16262FF",
    prop: "k4",
    list: [],
  },
]);
const pageRefData = ref({
  section1: {},
  tip: {
    visible: false,
    detail: [],
    titles: markRaw([
      {
        label: "序号",
        prop: "k1",
        dir: "center",
        width: 1,
      },
      {
        label: "员工名称",
        prop: "k2",
        width: 2,
      },
      {
        label: "工单数量",
        prop: "k3",
        width: 1,
      },
    ]),
    tableData: markRaw([
      {
        k1: "1",
        k2: "王小五",
        k3: "5",
      },
      {
        k1: "1",
        k2: "王小五",
        k3: "5",
      },
    ]),
  },
});
const msg = ref("");
const apisMap = new Map([
  [
    "getWorkOrderAnalysis",
    {
      api_pro: getWorkOrderAnalysis,
      params: {
        day: "7",
      },
      callback(handledData) {
        msg.value = handledData.code == "00000" ? "请求成功" : "请求失败";
        viewData.value = viewData.value.map((item) => {
          const itemData = handledData[item.prop];
          return {
            ...item,
            val: itemData.val,
            percent: itemData.percent,
            info: handledData[item.prop + "_list"],
          };
        });
      },
    },
  ],
]);

const handles = {
  section1ClickHandle(item) {
    const tip = pageRefData.value.tip;
    tip.tableData = (item.info || []).map((item, index) => {
      return {
        k1: index + 1,
        k2: item.name,
        k3: item.number,
      };
    });
    tip.visible = true;
  },
};
const currentSwitch = ref("7");
// 页面switch切换
const handleSwitchChange = async (item) => {
  currentSwitch.value = item.key;
  await getOrder(true);
};

const getData = async (flag) => {
  if (flag) {
    loading.value = true;
    await patchReq([...apisMap.values()]);
    loading.value = false;
  } else {
    patchReq([...apisMap.values()]);
  }
};
const getOrder = async (flag) => {
  try {
    flag && (loading.value = true);
    const { handledData, res } = await getWorkOrderAnalysis({
      day: currentSwitch.value,
    });
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
    const list = viewData.value.map((item) => {
      const itemData = handledData[item.prop];
      return {
        ...item,
        val: itemData.val,
        percent: itemData.percent,
        info: handledData[item.prop + "_list"],
      };
    });
    const container = document.getElementById("order-container");
    container.innerHTML = "";
    const p11 = getPage1Img('p11.png')
    const p58 = getPage1Img('p58.png')    
    list.forEach((item) => {
      const itemData = handledData[item.prop] || { val: 0, percent: "0%" };
      const wrapper = document.createElement("div");
      const imgUrl = getPage1Img(item.icon_class)      
      wrapper.className = "flex flex-col items-center cus-use-click";
      wrapper.addEventListener("click", () => handles.section1ClickHandle(item));
      wrapper.innerHTML = `
      <div style="background-image: url(${p11})" class="w-[348px] h-[219px] cus-bg-full flex cus-flex-center relative">
        <div class="cus-bg-full absolute top-0" style="width:${item.icon_style.w}; height:${item.icon_style.h};background-image: url(${imgUrl})"></div>
      </div>
      <div class="text-center my-[22px] text-[42px]" style="color:${item.color}">
        ${item.title}
      </div>
      <div style="background-image: url(${p58})" class="w-[330px] h-[96px] cus-bg-full  flex justify-center items-center whitespace-nowrap">
        <span class="text-[40px] text-[#CBDDF2FF]">
          <span class="text-[40px] text-[#CBDDF2FF] font-medium mr-[8px]">${itemData.val}</span>个
        </span>
        <div class="w-[2px] h-[30px] bg-[#CBDDF2FF] mx-[10px]"></div>
        <span>${itemData.percent}</span>
      </div>
    `;
      container.appendChild(wrapper);
    });
  } catch (error) {
    console.log(error, "err");
    msg.value = "请求失败";
  } finally {
    loading.value = false;
  }
};
let task = null
onMounted(() => {
  // getData();
  getOrder(true);
  task = ()=>getOrder(true)
  pollingManager.register(task)
});
onUnmounted(() => {
  pollingManager.unregister(task)
})
</script>

<style lang="less" scoped>
.bg {
  background-image: url("@/assets/imgs/page1/p64.png");
  background-repeat: no-repeat;
  background-position: 0% 0%;
  background-size: cover;
}
.page1-p88 {
  background-image: url("@/assets/imgs/page1/p88.png");
}

.page1-p89 {
  background-image: url("@/assets/imgs/page1/p89.png");
}

.page1-p6 {
  background-image: url("@/assets/imgs/page1/p6.png");
}

.page1-p90 {
  background-image: url("@/assets/imgs/page1/p90.png");
}
</style>
