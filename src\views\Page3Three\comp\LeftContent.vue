<template>
  <div class="text-[40px]">
    <!-- <div class="flex gap-[20px]">
      <div class="h-[2762px] flex flex-col gap-[30px]">

      </div>
      <div class="h-[2762px] flex flex-col gap-[30px]">
        
      </div>
    </div> -->
    <div class="flex justify-between h-[1198px]">
      <div class="h-full w-[880px] flex flex-col justify-between">
        <PanelTitle
          v-loading="loading1"
          title="告警设备排名TOP5"
          :element-loading-text="msg"
        >
          <div class="w-[880px] h-[630px] pt-[104px] px-[56px]">
            <MySwitch
              :options="pageStaticData.section1.ops"
              val="30"
              class="justify-center"
              @switchChange="
                (item) => handleSwitchChange(item, 'getAlarmDeviceTop5')
              "
            />
            <div class="flex flex-col gap-y-[20px] mt-[36px]">
              <div
                v-for="(item, index) in pageRefData.section1"
                :key="item.name + index"
                class="flex items-center h-[54px] w-full hover:cursor-pointer"
                @click="pageHandlers.section1ClickHandle(item)"
              >
                <span
                  class="w-[54px] h-full bg-[url('@/assets/imgs/page3/p23.png')] cus-bg-full cus-flex-center text-[32px] font-medium text-[#FAF1E4FF]"
                  >{{ index + 1 }}</span
                >
                <div
                  class="ml-[6px] h-full w-[572px] bg-[url('@/assets/imgs/page3/p15.png')] cus-bg-full flex items-center"
                >
                  <span
                    class="w-[220px] inline-block text-[#CBDDF2CC] text-[36px] ml-[16px] truncate"
                  >
                    {{ item.name }}
                  </span>
                  <div class="h-[16px] grow mx-[20px] flex items-center">
                    <div
                      class="w-[50%] h-full bg-gradient-to-r from-[#0091FFFF] to-[#00EAFFFF]"
                      :style="{
                        width: `${item.val}`,
                      }"
                    ></div>
                    <span
                      class="w-[6px] h-[28px] mr-auto bg-[#CBDDF2FF]"
                    ></span>
                  </div>
                </div>
                <span
                  class="w-[124px] ml-auto text-[#CBDDF2FF] font-medium h-full cus-flex-center h-full bg-[url('@/assets/imgs/page3/p56.png')] cus-bg-full text-[36px]"
                  >{{ item.val }}</span
                >
              </div>
            </div>
          </div>
          <Refresh @click="getData('l1')" class="right-[100px] top-[30px]" />
        </PanelTitle>
        <PanelTitle
          v-loading="loading2"
          title="告警同环比分析"
          :element-loading-text="msg"
        >
          <div class="w-[880px] h-[530px] pt-[126px] px-[56px] relative">
            <!-- 切换选择 -->
            <!-- <div
              class="absolute top-[26px] right-[56px] w-[196px] h-[56px] bg-[url('@/assets/imgs/page3/p13.png')] cus-bg-full flex items-center text-[32px] font-medium">
              <span class="grow cus-flex-center h-full hover:cursor-pointer" v-for="item in pageStaticData.section2"
                :key="item" :class="[pageRefData.section2 === item ? 'ac' : 'ac_no']"
                @click="section2ChangeHandle(item)">
                {{ item }}</span>
            </div> -->
            <div class="flex justify-between relative">
              <div
                v-for="item in pageRefData.section3"
                :key="item.type"
                class="flex flex-col items-center w-[326px]"
              >
                <div
                  class="w-full h-[52px] bg-[url('@/assets/imgs/page3/p14.png')] cus-bg-full flex cus-flex-center text-[32px] font-medium"
                >
                  {{ item.label }}
                </div>
                <div
                  class="w-[280px] h-[166px] cus-bg-full mt-[28px]"
                  :class="[item.dir === 'up' ? 'up-bg' : 'down-bg']"
                ></div>
                <div
                  class="flex items-center mt-[30px] gap-x-[20px] text-[56px] text-bold"
                >
                  <span class="text-[42px] text-nowrap">{{ item.type }}</span>
                  <span class="text-[#75F8EEFF] font-bold lining-nums">{{
                    item.val
                  }}</span>
                </div>
              </div>
              <div
                class="h-[294px] w-[4px] absolute left-1/2 -translate-x-1/2 top-[10px] to-bottom-line"
              ></div>
            </div>
          </div>
          <Refresh @click="getData('l2')" class="right-[100px] top-[30px]" />
        </PanelTitle>
      </div>

      <PanelTitle
        v-loading="loading3"
        title="告警分析"
        :element-loading-text="msg"
      >
        <div class="w-[880px] h-[1198px] pt-[126px] px-[56px]">
          <!-- 当月告警分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p47.png')] cus-bg-full"
          ></div>
          <div
            class="w-full h-[332px] mt-[40px] flex flex-wrap gap-x-[120px] justify-between content-between relative"
          >
            <div
              v-for="item in pageRefData.section4"
              :key="item.label"
              class="w-[266px] flex flex-col items-center cus-use-click"
              @click="pageHandlers.section4ClickHandle(item)"
            >
              <span class="text-[#CBDDF2FF] tracking-[4px]">{{
                item.label
              }}</span>
              <div
                class="w-full h-[104px] bg-[url('@/assets/imgs/page3/p51.png')] cus-bg-full mt-[10px] relative cus-flex-center text-[#CBDDF2FF]"
              >
                <div class="absolute top-[14px]">
                  <span class="font-medium">{{ item.val }}</span>
                  <span class="text-[36px] ml-[16px]">次</span>
                </div>
              </div>
            </div>

            <!-- center 图标 -->
            <div class="absolute left-1/2 -translate-x-1/2 top-[18px]">
              <div class="relative w-[174px] h-[174px] cus-flex-center">
                <div
                  class="absolute w-full h-full bg-[url('@/assets/imgs/page3/p29.png')] cus-bg-full animate-turn2"
                ></div>
                <div
                  class="absolute w-[146px] h-[146px] bg-[url('@/assets/imgs/page3/p31.png')] cus-bg-full animate-turn"
                ></div>
                <div
                  class="absolute w-[115px] h-[114px] bg-[url('@/assets/imgs/page3/p28.png')] cus-bg-full animate-turn"
                ></div>
                <div
                  class="absolute w-[60px] h-[60px] bg-[url('@/assets/imgs/page3/p20.png')] cus-bg-full"
                ></div>
              </div>
              <div class="text-[#CBDDF2FF] mt-[16px]">告警次数</div>
            </div>
          </div>
        </div>
        <Refresh @click="getData('l3')" class="right-[100px] top-[30px]" />
      </PanelTitle>
    </div>

    <div class="flex justify-between h-[1528px] mt-[64px]">
      <div class="w-[880px] h-full flex flex-col justify-between">
        <PanelTitle
          v-loading="loading5"
          title="当月告警处置分析"
          :element-loading-text="msg"
        >
          <div class="h-[838px] w-[880px] px-[56px] pt-[130px]">
            <div class="cus-flex-center">
              <div class="w-[408px] h-[408px] relative flex cus-flex-center">
                <Echart :option="pageRefData.options.option4" />
                <div class="absolute text-center">
                  <div class="text-[#CBDDF2FF] text-[52px] font-medium">
                    {{ pageRefData.section6[0].val }}
                  </div>
                  <div class="mt-[10px] text-[#CBDDF2FF] text-[36px]">
                    处理中
                  </div>
                </div>
              </div>
            </div>
            <div class="flex justify-around mt-[74px]">
              <div
                v-for="item in pageRefData.section6"
                :key="item.key"
                class="flex items-center hover:cursor-pointer"
                @click="pageHandlers.section6ClickHandle(item)"
              >
                <div
                  class="h-[96px] w-[4px] cus-bg-full"
                  :style="{
                    backgroundImage: `url('${item.b_img}')`,
                  }"
                ></div>
                <div class="flex flex-col ml-[16px]">
                  <span class="text-[#CBDDF2CC] text-[36px]">{{
                    item.label
                  }}</span>
                  <span class="font-medium mt-[20px]">{{ item.val }}</span>
                </div>
              </div>
            </div>
          </div>
          <Refresh @click="getData('l5')" class="right-[100px] top-[30px]" />
        </PanelTitle>
        <PanelTitle
          v-loading="loading6"
          title="近一年告警趋势分析"
          :element-loading-text="msg"
        >
          <div class="h-[652px] w-[880px] px-[56px] pt-[124px]">
            <div class="h-[450px] w-full">
              <Echart :option="pageRefData.options.option5" />
            </div>
          </div>
          <Refresh @click="getData('l6')" class="right-[100px] top-[30px]" />
        </PanelTitle>
      </div>
      <PanelTitle
        v-loading="loading4"
        title="趋势分析3"
        :element-loading-text="msg"
      >
        <div class="h-[1528px] w-[880px] px-[56px] pt-[126px]">
          <!-- 温度趋势分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p45.png')] cus-bg-full"
          ></div>
          <div class="w-full h-[320px] mt-[24px]">
            <Echart :option="pageRefData.options.option1" />
          </div>
          <!-- 湿度趋势分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p48.png')] cus-bg-full mt-[32px]"
          ></div>
          <div class="w-full h-[320px] mt-[24px]">
            <Echart :option="pageRefData.options.option2" />
          </div>
          <!-- 含氧量趋势分析 -->
          <div
            class="w-full h-[72px] bg-[url('@/assets/imgs/page3/p46.png')] cus-bg-full mt-[32px]"
          ></div>
          <div class="w-full h-[320px] mt-[24px]">
            <Echart :option="pageRefData.options.option3" />
          </div>
        </div>
        <Refresh @click="getEnv(true)" class="right-[100px] top-[30px]" />
      </PanelTitle>
    </div>

    <!-- 告警设备排名点击弹窗 -->
    <My-Tip
      stl="left-[2384px] top-[574px]"
      v-model:visible="pageRefData.tips.tip1.visible"
    >
      <div
        v-for="item in pageRefData.tips.tip1.detail"
        :key="item.label"
        class="text-[36px]"
      >
        <span>{{ item.label }}</span> : <span>{{ item.value }}</span>
      </div>
    </My-Tip>

    <!-- 当月AI告警分析 -->
    <!-- <My-Tip
      stl="left-[2155px] top-[1030px]"
      v-model:visible="pageRefData.tips.tip2.visible"
    >
      <div class="w-[864px] h-[374px]">
        <C-Table
          :titles="pageRefData.tips.tip2.titles"
          :table-data="pageRefData.tips.tip2.tableData"
        ></C-Table>
      </div>
    </My-Tip> -->
    <My-Tip
      stl="cs-t1 top-[904px]"
      v-model:visible="pageRefData.tips.tip2.visible"
      show-mask
      close
      center
      :auto-close="false"
    >
      <div
        v-loading="tableLoading"
        element-loading-background="rgba(0,0,0,.5)"
        class="w-[2548px] h-[1738px] cus-bg-full bg-[url('@/assets/imgs/page1/p115.png')] ccc"
      >
        <div class="ct4 w-[full] h-[1248px] mt-[40px] pl-[64px] pr-[52px]">
          <C-Table
            :titles="pageRefData.tips.tip2.titles"
            :table-data="tableData"
            @item-click="itemClick"
            max-height="1300px"
            gap="28px"
          >
            <template #k6="{ row }">
              <span
                v-if="row.k6 !== null && row.k6 !== undefined"
                class="inline-block w-[48px] h-[48px] cus-flex-center"
                :class="handleWaning(row.k6)?.class"
              >
                {{ handleWaning(row.k6)?.label }}1
              </span>
              <span v-else>-1</span>
            </template>
          </C-Table>
        </div>
        <div class="flex justify-center">
          <el-pagination
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            :page-sizes="paginationConfig.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @size-change="sizeChange"
            @current-change="currentChange"
            class="scale-[2.6] mt-[320px]"
            locale="zh-cn"
          />
        </div>
      </div>
    </My-Tip>
    <!-- 区域告警分析 -->
    <My-Tip
      stl="left-[2229px] top-[1556px]"
      v-model:visible="pageRefData.tips.tip3.visible"
    >
      <div class="w-[546px] h-[308px]">
        <C-Table
          :titles="pageRefData.tips.tip3.titles"
          :table-data="pageRefData.tips.tip3.tableData"
        ></C-Table>
      </div>
    </My-Tip>

    <!-- 当月告警处置分析 -->
    <My-Tip
      stl="left-[2228px] top-[2146px]"
      v-model:visible="pageRefData.tips.tip4.visible"
    >
      <div class="text-[38px]">告警处置详情</div>
      <div class="flex gap-x-[80px] justify-center mt-[32px]">
        <div v-for="item in pageRefData.tips.tip4.detail" class="text-[36px]">
          <div class="#CBDDF2FF font-medium">
            <span>{{ item.count }}</span> | <span>{{ item.point }}</span>
          </div>
          <div
            class="px-[44px] py-[12px] rounded-[4px] mt-[20px] cus-flex-center"
            :class="[pageRefData.tips.tip4.getStatusClass(item.typeLevel)]"
          >
            {{ item.typeLevel }}
          </div>
        </div>
      </div>
    </My-Tip>
  </div>
</template>

<script setup>
import { ref, onMounted, markRaw, watch } from "vue";
import { getImg } from "@/utils/assets";
import {
  createChartOp1,
  createChartOp1B,
  createChartOp2,
  createChartOp3,
} from "./chart-options/chart1";
import {
  getAlarmComparisonAnalysis,
  getAlarmDeviceTop5,
  getMonthAlarmDisposalAnalysis,
  getYearAlarmTrendAnalysis,
  getMonthAlarmAnalysisAI,
  getAreaAlarmAnalysis,
  environmentAnalysisData,
} from "@/axios/apis/page3";
import patchReq from "@/utils/patchReq";
import { useStore } from "vuex";
import request from "@/axios/request";
import { handleWaning } from "@/utils";
const props = defineProps({
  // props
});
const tableData = ref([]);
const tableLoading = ref(false);
const store = useStore();
const msg = ref("");
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const loading4 = ref(false);
const loading5 = ref(false);
const loading6 = ref(false);
const paginationConfig = ref({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 15, 30, 50],
  total: 80,
  disabled: false,
});
const itemClick = (item) => {
  console.log(item);
};
const sizeChange = (size) => {
  console.log(size, "size");
  paginationConfig.value.pageSize = size;
};
const currentChange = (page) => {
  console.log(page, "page");
  paginationConfig.value.currentPage = page;
};
const pageStaticData = {
  section1: {
    ops: [
      {
        label: "近七天",
        key: "7",
      },
      {
        label: "近30天",
        key: "30",
      },
      {
        label: "近半年",
        key: "180",
      },
    ],
  },
  section2: ["3月", "季度"],
  section3: [
    {
      label: "2023/3  vs  2024/3",
      type: "同比",
      val: "2.3%",
      dir: "up",
    },
    {
      label: "2023/2  vs  2023/3",
      type: "环比",
      val: "2.2%",
      dir: "down",
    },
  ],
};
const section2ChangeHandle = (val) => {
  pageRefData.value.section2 = val;
};
const setOption = (res) => {
  options.option3 = res["氧含量检测仪"];
  options.option2 = res["温湿度检测仪(温度)"];
  options.option1 = res["温湿度检测仪(湿度)"];
};
const getTableData = async () => {
  try {
    tableLoading.value = true;
    const _params = {
      current: paginationConfig.value.currentPage,
      size: paginationConfig.value.pageSize,
      query: {
        code: code.value,
      },
    };
    const res = await request(
      "/admin/pipe/monitor/alarm/findZhjkAlarmInfo",
      _params,
      "POST"
    );
    paginationConfig.value.total = res.data.total;
    const data = res?.data?.records || [];
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
    tableData.value = data.map((i, index) => {
      return {
        ...i,
        k1: (_params.current - 1) * _params.size + index + 1,
        k2: i.equipmentName,
        k3: i.area,
        k4: i.alarmDescription,
        k5: i.alarmTime,
        k6: i.alarmLevel,
      };
    });
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    tableLoading.value = false;
  }
};
watch(
  () => [paginationConfig.value.pageSize, paginationConfig.value.currentPage],
  getTableData
);
const pageRefData = ref({
  section1: markRaw([
    {
      name: "湿温度监测仪",
      val: "10%",
    },
    {
      name: "甲烷含量监测",
      val: "20%",
    },
    {
      name: "烟感探头",
      val: "50%",
    },
    {
      name: "手报",
      val: "80%",
    },
    {
      name: "话机",
      val: "100%",
    },
  ]),
  section2: "季度",
  section3: markRaw([
    {
      type: "环比",
      label: "2024-04 vs 2024-05",
      val: "-100.0%",
      dir: "down",
    },
    {
      type: "同比",
      label: "2023-05 vs 2024-05",
      val: "0.00%",
      dir: "up",
    },
  ]),
  section4: markRaw([
    {
      label: "廊内",
      val: 1,
      key: "key0",
    },
    {
      label: "监控中心",
      val: 0,
      key: "key1",
    },
    {
      label: "地面",
      val: 0,
      key: "key2",
    },
  ]),
  section5: markRaw([
    {
      label: "综合舱",
      val: "1000",
      key: "k1",
    },
    {
      label: "电力舱",
      val: "1000",
      key: "k2",
    },
    {
      label: "进/出排风口",
      val: "1000",
      key: "k3",
    },
    {
      label: "人员出入口",
      val: "1000",
      key: "k4",
    },
  ]),
  options: {
    option1: {},
    option2: {},
    option3: {},
    option4: {},
    option5: {},
  },
  section6: markRaw([
    {
      label: "处理中",
      val: "30%",
      key: "k1",
      b_img: getImg("page3/p62.png"),
    },
    {
      label: "已处理",
      val: "30%",
      key: "k2",
      b_img: getImg("page3/p1.png"),
    },
    {
      label: "未处理",
      val: "30%",
      key: "k3",
      b_img: getImg("page3/p2.png"),
    },
  ]),
  section7: markRaw({
    type1: {
      name: "环比",
      pre: "2024-04",
      cur: "2024-05",
      value: "-100.0%",
      dir: "down",
    },
    type2: {
      name: "同比",
      pre: "2023-05",
      cur: "2024-05",
      value: "0.00%",
      dir: "up",
    },
  }),
  tips: {
    tip1: {
      visible: false,
      detail: [],
    },
    tip2: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 2,
        },
        {
          label: "告警设备",
          prop: "k2",
          width: 3.2,
        },
        {
          label: "告警区域",
          prop: "k3",
          width: 3.2,
        },
        {
          label: "告警详情",
          prop: "k4",
          width: 2.2,
        },
        {
          label: "告警时间",
          prop: "k5",
          width: 3.2,
        },
        {
          label: "告警等级",
          prop: "k6",
          width: 2,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "廊内",
          k3: "综合舱",
          k4: "2024-04-10 ",
        },
        {
          k1: "2",
          k2: "监控中心",
          k3: "电力舱",
          k4: "2024-04-10 ",
        },
      ]),
    },
    tip3: {
      visible: false,
      detail: [],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          dir: "center",
          width: 1.2,
        },
        {
          label: "区域",
          prop: "k2",
          width: 2,
        },
        {
          label: "告警次数",
          prop: "k3",
          width: 2,
        },
      ]),
      tableData: markRaw([
        {
          k1: "1",
          k2: "廊内",
          k3: "3",
        },
        {
          k1: "2",
          k2: "监控中心",
          k3: "3",
        },
      ]),
    },
    tip4: {
      visible: false,
      detail: [],
      getStatusClass(level) {
        const map = new Map([
          ["高", "warning-heigh"],
          ["中", "warning-medium"],
          ["低", "warning-lower"],
        ]);
        return map.get(level) || "warning-lower";
      },
    },
  },
});

const options = pageRefData.value.options;
const currentDay = ref("30");
const apisMap = new Map([
  [
    "getAlarmComparisonAnalysis",
    {
      api_pro: getAlarmComparisonAnalysis,
      params: {},
      callback(handledData) {
        msg.value = handledData.code == "00000" ? "请求成功" : "请求失败";
        pageRefData.value.section3 = handledData;
      },
    },
  ],
  [
    "getAlarmDeviceTop5",
    {
      api_pro: getAlarmDeviceTop5,
      params: {
        day: "30",
      },
      callback(handledData) {
        msg.value = handledData.code == "00000" ? "请求成功" : "请求失败";
        pageRefData.value.section1 = handledData;
      },
    },
  ],
  [
    "getMonthAlarmDisposalAnalysis",
    {
      api_pro: getMonthAlarmDisposalAnalysis,
      callback(res) {
        const { handledData, optionData } = res;
        const transformData = pageRefData.value.section6.map((item) => {
          item.val = handledData[item.key];
          item.detail = handledData[item.key + "_detail"];
          return item;
        });
        console.log(handledData, optionData, "getMonthAlarmDisposalAnalysis");

        options.option4 = markRaw(createChartOp2(optionData));
        pageRefData.value.section6 = transformData;
      },
    },
  ],
  [
    "getYearAlarmTrendAnalysis",
    {
      api_pro: getYearAlarmTrendAnalysis,
      callback(res) {
        const { optionData } = res;
        options.option5 = markRaw(createChartOp3(optionData));
      },
    },
  ],
  [
    "getMonthAlarmAnalysisAI",
    {
      api_pro: getMonthAlarmAnalysisAI,
      params: {},
      callback(handledData) {
        pageRefData.value.section4 = handledData;
      },
    },
  ],
  [
    "getAreaAlarmAnalysis",
    {
      api_pro: getAreaAlarmAnalysis,
      callback(handledData) {
        pageRefData.value.section5 = handledData;
      },
    },
  ],
  // [
  //   "environmentAnalysisData",
  //   {
  //     api_pro: environmentAnalysisData,
  //     params: {},
  //     callback: setOption,
  //   },
  // ],
]);
const getEnv = (flag) => {
  const [code = "zhc", num = 1] = store.state.pageTwoCode;
  loading4.value = true;
  environmentAnalysisData({
    partitionCode: "tonghenanlu_fenqu" + num + "_" + code,
  })
    .then((res) => {
      msg.value = res.code == "00000" ? "请求成功" : "请求失败";
      setOption(res);
    })
    .finally(() => {
      loading4.value = false;
    });
};
watch(() => store.state.pageTwoCode, getEnv, {
  immediate: true,
});
watch(
  () => pageRefData.value.tips.tip2.visible,
  (val) => {
    if (!val) {
      Object.assign(paginationConfig.value, {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 15, 30, 50],
        total: 80,
        disabled: false,
      });
    }
  }
);
const getData = async (type) => {
  // 设置默认图表配置
  // options.option1 = markRaw(createChartOp1B());
  // options.option2 = markRaw(createChartOp1());
  // options.option3 = markRaw(createChartOp1());
  options.option4 = markRaw(createChartOp2());
  options.option5 = markRaw(createChartOp3());
  const obj = {
    l1: { loading: loading1, keys: ["getAlarmDeviceTop5"] },
    l2: { loading: loading2, keys: ["getAlarmComparisonAnalysis"] },
    l3: {
      loading: loading3,
      keys: ["getMonthAlarmAnalysisAI", "getAreaAlarmAnalysis"],
    },
    // l4: { loading: loading4, keys: ["environmentAnalysisData"] },
    l5: { loading: loading5, keys: ["getMonthAlarmDisposalAnalysis"] },
    l6: { loading: loading6, keys: ["getYearAlarmTrendAnalysis"] },
  };

  const item = obj[type];
  if (item?.loading) item.loading.value = true;
  if (!type) {
    await patchReq([...apisMap.values()]);
  } else {
    const apis = (item.keys || [])
      .map((key) => {
        const i = apisMap.get(key);
        if (key == "getAlarmDeviceTop5") {
          i.params.day = currentDay.value;
        }
        return i;
      })
      .filter(Boolean);
    const res = await patchReq(apis);
    msg.value = res.every((i) => i.code == "00000") ? "请求成功" : "请求失败";
  }

  // 结束 loading 状态
  if (item?.loading) item.loading.value = false;
};
const code = ref("");
// 页面switch切换
const handleSwitchChange = async (item, apiName) => {
  currentDay.value = item.key;
  const apiConfig = apisMap.get(apiName);
  if (apiConfig) {
    const res = await apiConfig.api_pro({
      day: item.key,
    });
    apiConfig.callback(res);
  }
};
const pageHandlers = {
  section1ClickHandle(item) {
    pageRefData.value.tips.tip1.visible = true;
    pageRefData.value.tips.tip1.detail = markRaw(
      item.detail || [
        {
          label: "设备名称",
          value: "温湿度监测仪",
        },
        {
          label: "设备编码",
          value: "JK12CK93",
        },
        {
          label: "告警次数",
          value: "2",
        },
        {
          label: "告警时间",
          value: "2024-07-10 12:03:18",
        },
      ]
    );
  },
  section4ClickHandle(data) {
    const { detail = {} } = data;
    code.value = detail.cabinCode;
    getTableData();
    pageRefData.value.tips.tip2.visible = true;
  },
  section5ClickHandle({ detail = {} }) {
    const list = detail["regionVOs"] || [];
    pageRefData.value.tips.tip3.tableData = markRaw(
      list.map((item, index) => ({
        k1: index + 1,
        k2: item.areaName,
        k3: item.areaNumber,
      }))
    );

    pageRefData.value.tips.tip3.visible = true;
  },
  section6ClickHandle(item) {
    pageRefData.value.tips.tip4.visible = true;
    pageRefData.value.tips.tip4.detail = markRaw(item.detail || []);
  },
};

onMounted(() => {
  getData();
});
</script>

<style lang="less" scoped>
.ac {
  background-image: url("@/assets/imgs/page3/p12.png");
  color: #cbddf2ff;
}

.ac_no {
  background-image: none;
  color: #cbddf299;
}

.up-bg {
  background-image: url("@/assets/imgs/page3/p50.png");
}

.down-bg {
  background-image: url("@/assets/imgs/page3/p49.png");
}

.to-bottom-line {
  background-image: repeating-linear-gradient(
    to bottom,
    rgba(181, 196, 214, 0.829),
    rgba(181, 196, 214, 0.829) 6px,
    transparent 6px,
    transparent 12px
  );
}

.warning-heigh {
  background-color: rgba(255, 34, 0, 0.15);
  color: #ff7184;
}

.warning-medium {
  background-color: rgba(255, 153, 0, 0.15);
  color: #fdff8b;
}

.warning-lower {
  background-color: rgba(0, 209, 255, 0.15);
  color: #00d1ff;
}

:deep(.c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
    background-image: initial;
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    background-image: initial;
  }
}
:deep(.el-pagination__total) {
  color: #fff !important;
}
.ccc {
  :deep(.el-pagination) {
    color: #fff;

    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }

    .el-input__wrapper {
      background-color: #49779299;
    }

    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }

    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-select__icon {
      color: white;
    }

    .el-select__tags {
      top: 20%;

      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
:deep(.ct4 .c-table) {
  .c-table-head {
    color: rgba(203, 221, 242, 1);
    font-size: 40px;
    margin-top: 140px;
  }

  .c-table-row {
    color: rgba(203, 221, 242, 1);
    pointer-events: auto;
    height: 120px;
    font-size: 35px;
    &:hover {
      cursor: pointer;
    }
  }
}
</style>
