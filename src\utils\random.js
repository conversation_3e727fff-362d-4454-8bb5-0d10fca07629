
import dayjs from "dayjs";
export function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function generateRandomNumber(min, max) {
  // 生成指定区间内的随机数
  var randomNumber = Math.random() * (max - min) + min;
  // 将随机数精确到后一位
  var roundedNumber = Math.round(randomNumber * 10) / 10;
  return roundedNumber;
}

/**
 * 获取最近天数
 * @date 2023-08-25
 * @param {any} num=6
 * @param {any} g='MM-DD'
 * @returns {any}
 */
export function getDays(num = 6, g = 'MM-DD') {
  const today = dayjs(); // 当前日期
  const recentDates = [];
  for (let i = num-1; i > -1; i--) {
    const date = today.subtract(i, 'day');
    recentDates.push(date.format(g));
  }
  return recentDates
}