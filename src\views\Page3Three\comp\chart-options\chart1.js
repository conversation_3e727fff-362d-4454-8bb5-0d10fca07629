import * as echarts from "echarts";
import formatDateTime from "@/utils/formatDateTime";

export const createChartOp1 = () => {
  let data = [
    { time: "2024.04.24 00:00:00", value: 480 },
    { time: "2024.04.25 00:00:00", value: 500 },
    { time: "2024.04.26 00:00:00", value: 450 },
    { time: "2024.04.27 00:00:00", value: 520 },
    { time: "2024.04.28 00:00:00", value: 490 },
    { time: "2024.04.29 00:00:00", value: 550 },
    { time: "2024.04.30 00:00:00", value: 580 },
    { time: "2024.05.01 00:00:00", value: 560 },
    { time: "2024.05.02 00:00:00", value: 530 },
    { time: "2024.05.03 00:00:00", value: 590 },
    { time: "2024.05.04 00:00:00", value: 540 },
    { time: "2024.05.05 00:00:00", value: 600 },
    { time: "2024.05.06 00:00:00", value: 620 },
    { time: "2024.05.07 00:00:00", value: 650 },
    { time: "2024.05.08 00:00:00", value: 680 },
    { time: "2024.05.09 00:00:00", value: 630 },
    { time: "2024.05.10 00:00:00", value: 660 },
    { time: "2024.05.11 00:00:00", value: 690 },
    { time: "2024.05.12 00:00:00", value: 420, predict: true },
    { time: "2024.05.13 00:00:00", value: 450, predict: true },
    { time: "2024.05.14 00:00:00", value: 480, predict: true },
    { time: "2024.05.15 00:00:00", value: 510, predict: true },
    { time: "2024.05.16 00:00:00", value: 540, predict: true },
    { time: "2024.05.17 00:00:00", value: 480, predict: true },
    { time: "2024.05.18 00:00:00", value: 510, predict: true },
    { time: "2024.05.19 00:00:00", value: 630, predict: true },
    { time: "2024.05.20 00:00:00", value: 660, predict: true },
    { time: "2024.05.21 00:00:00", value: 690, predict: true },
    { time: "2024.05.22 00:00:00", value: 650, predict: true },
    { time: "2024.05.23 00:00:00", value: 750, predict: true },
    { time: "2024.05.24 00:00:00", value: 780, predict: true },
  ];

  let data1 = [];
  let data2 = [];
  const getDateStr = (dateStr) => formatDateTime(new Date(dateStr), "MM.DD");

  data.forEach((item) => {
    if (item.predict) {
      data2.push([getDateStr(item.time), item.value]);
    } else {
      data1.push([getDateStr(item.time), item.value]);
    }
  });
  const splitLineData = data1.at(-1);

  // 如果是折线图，此处需要追加实际数据的最后一组数据，如果是柱状图，则不需要。
  data2.unshift(splitLineData);

  const labels = data.map((m) => {
    return getDateStr(m.time);
  });

  const option = {
    grid: {
      left: 0,
      top: "22%",
      right: "4%",
      bottom: 0,
      containLabel: true,
    },
    legend: {
      right: "0",
      top: "4%",
      textStyle: {
        fontSize: 24,
        color: "rgba(203, 221, 242, 1)",
      },
    },
    xAxis: {
      type: "category",
      data: labels,
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
      },
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
    },
    series: [
      {
        name: "历史",
        type: "line",
        showSymbol: false,
        data: data1,
        smooth: true,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 0.6,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 170, 255, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 170, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          width: 2,
          color: "rgba(0, 170, 255, 1)",
        },
      },
      {
        name: "预测",
        type: "line",
        showSymbol: false,
        data: data2,
        smooth: true,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 0.6,
            colorStops: [
              {
                offset: 0,
                color: "rgba(46, 224, 85, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(46, 224, 85, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          width: 2,
          color: "rgba(46, 224, 85, 1)",
        },
      },
      {
        type: "line",
        data: [],
        markLine: {
          symbol: "none",
          lineStyle: {
            type: "solid",
            width: 2,
          },
          data: [
            // 灰色线
            {
              xAxis: splitLineData[0],
              label: {
                show: false,
              },
              lineStyle: {
                color: "rgba(203, 221, 242, 1)",
              },
            },
            // 下面绿色线
            [
              // 下面半截绿色的线
              {
                xAxis: splitLineData[0],
                yAxis: 0,
                lineStyle: {
                  color: "rgba(46, 224, 85, 1)",
                },
              },
              {
                xAxis: splitLineData[0],
                yAxis: splitLineData[1],
              },
            ],
          ],
        },
        markPoint: {
          data: [
            {
              coord: splitLineData,
              symbol: "circle",
              symbolSize: 16,
              itemStyle: {
                color: "rgba(203, 221, 242, 1)",
                shadowColor: "rgba(46, 224, 85, 1)",
                shadowBlur: 10,
              },
            },
          ],
        },
      },
    ],
  };

  return option;
};

export const createChartOp1B = () => {
  let data = [
    [
      ["2024-08-12 10:39:34", 61.2],
      ["2024-08-12 10:39:54", 61.2],
      ["2024-08-12 10:40:25", 26.5],
      ["2024-08-12 10:41:20", 6.14],
    ],
    [
      ["2024-08-12 10:39:09", 21],
      ["2024-08-12 10:39:21", 21],
      ["2024-08-12 10:40:05", 27],
      ["2024-08-12 10:40:44", 11],
      ["2024-08-12 10:41:41", 32],
    ],
  ];

  function formatDateTime(dateP, format) {
    const pad = (n) => (n < 10 ? "0" + n : n); // 补零函数
    const date = new Date(dateP);
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());

    return format
      .replace("YYYY", year)
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hours)
      .replace("mm", minutes)
      .replace("ss", seconds);
  }
  const getDateStr = (dateStr) => formatDateTime(new Date(dateStr), "MM.DD");

  const series = data.map((itemData, index) => {
    return {
      name: "设备" + index,
      type: "line",
      showSymbol: false,
      data: itemData,
      smooth: true,
      lineStyle: {
        width: 4,
      },
    };
  });

  const option = {
    // backgroundColor: "#061c4c",
    grid: {
      left: 0,
      top: "22%",
      right: "4%",
      bottom: 0,
      containLabel: true,
    },
    legend: {
      right: "0",
      top: "4%",
      textStyle: {
        fontSize: 24,
        color: "rgba(203, 221, 242, 1)",
      },
    },
    xAxis: {
      type: "category",
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
      },
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
    },
    series,
  };
  return option;
};

export const createChartOp2 = (data) => {
  const pieData = data ?? [
    {
      name: "已处理",
      value: "3",
    },
    {
      name: "处理中",
      value: "7",
    },
    {
      name: "未处理",
      value: "9",
    },
  ];
  const option = {
    color: [
      "rgba(245, 217, 130,1)",
      "rgba(99, 142, 246,1)",
      "rgba(232, 141, 107,1)",
      "rgba(65, 219, 167,1)",
    ],
    series: [
      {
        type: "gauge",
        center: ["50%", "50%"],
        startAngle: 90,
        endAngle: -270,
        radius: "50%",
        axisLabel: {
          show: false,
        },
        anchor: {
          show: false,
        },
        title: {
          show: false,
        },
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
        progress: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 2,
          },
        },
        axisTick: {
          distance: -36,
          length: 14,
          splitNumber: 2,
          lineStyle: {
            width: 1,
            color: "rgba(203, 221, 242, 1)",
          },
        },
        splitNumber: 8,
        splitLine: {
          distance: -36,
          length: 14,
          lineStyle: {
            width: 2,
            color: "rgba(203, 221, 242, 1)",
          },
        },
        data: [100],
      },
      {
        color: ["#163d59"],
        type: "gauge",
        center: ["50%", "50%"],
        startAngle: 90,
        endAngle: -270,

        radius: "100%",
        pointer: {
          show: false,
        },
        progress: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        anchor: {
          show: false,
        },
        title: {
          show: false,
        },
        detail: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 1,
            color: [[1, "rgba(203, 221, 242, 1)"]],
          },
        },
        data: [100],
      },
      {
        name: "",
        type: "pie",
        radius: ["74%", "84%"],
        center: ["50%", "50%"],
        label: {
          show: false,
        },
        data: pieData,
      },
    ],
  };
  return option;
};

export const createChartOp3 = (data) => {
  const rawData = data ?? [
    {
      name: "02-28",
      value: 3,
    },
    {
      name: "03-07",
      value: 1,
    },
    {
      name: "03-08",
      value: 1,
    },
    {
      name: "03-14",
      value: 1,
    },
    {
      name: "04-13",
      value: 1,
    },
    {
      name: "04-16",
      value: 6,
    },
    {
      name: "04-19",
      value: 4,
    },
    {
      name: "04-26",
      value: 2,
    },
  ];
  const dataset = {
    dimensions: ["name", "value"],
    source: rawData,
  };
  const axisLabel = {
    fontSize: 28,
    color: "rgba(203, 221, 242, 0.60)",
  };

  const option = {
    dataset,
    tooltip: {
      trigger: "axis",
      axisPointer: {
        snap: true,
        type: "line",
        lineStyle: {
          color: "rgba(247, 249, 252, 0.30)", // 线的颜色
          width: 4, // 线宽
          type: "solid", // 线型
        },
      },
      formatter: function (params) {
        const item = params[0];
        return `${item.name}<br/>数量: ${item.value?.value} 次`;
      },
      backgroundColor: "rgba(27, 23, 21, 0.80)",
      borderWidth: 0,
      padding: 22,
      textStyle: {
        color: "#fff",
        fontSize: 40,
      },
      position: (point) => [point[0] + 20, point[1] - 10],
    },
    grid: {
      left: 0,
      bottom: 0,
      containLabel: true,
      right: 0,
      top: "20%",
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false, //隐藏X轴刻度
      },
      axisLabel,
    },
    yAxis: {
      type: "value",
      name: "次",
      nameTextStyle: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
        align: "center",
        padding: [0, 50, 10, 0],
      },
      axisLabel,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(223, 223, 223, 1)",
        },
      },
    },
    series: [
      {
        type: "line",
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 0.6,
            colorStops: [
              {
                offset: 0,
                color: "rgba(102, 146, 255, 0.5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(102, 146, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#6692FFFF",
        },
        // symbol: "none",
        symbol: "circle",
        symbolSize: 10,
        showSymbol: false,
        smooth: true,
        itemStyle: {
          color: "rgba(161, 173, 183, 0.8)",
          shadowColor: "rgba(255, 255, 255, 1)",
          shadowBlur: 10,
        },
      },
    ],
  };
  return option;
};

export const createChartOp4 = (data, colorList) => {
  const rawData = data ?? [
    {
      name: "电力舱",
      value: 123,
    },
    {
      name: "综合舱",
      value: 106,
    },
    {
      name: "人员出入口",
      value: 2,
    },
    {
      name: "进风口",
      value: 5,
    },
    {
      name: "排风口",
      value: 6,
    },
    {
      name: "逃生口",
      value: 0,
    },
  ];
  const dataset = {
    dimensions: ["name", "value"],
    source: rawData,
  };

  const option = {
    color: colorList,
    polar: {
      radius: ["26%", "86%"],
      center: ["50%", "50%"],
    },
    angleAxis: {
      max: (val) => val.max * 1.2,
      startAngle: 90,
      clockwise: false,
      splitNumber: 6,
      axisLine: {
        show: true,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: true,
      },
    },
    radiusAxis: {
      type: "category",
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    dataset,
    series: [
      {
        type: "bar",
        coordinateSystem: "polar",
        showBackground: true, // 展示背景阴影
        roundCap: true,
        barWidth: 6,
        colorBy: "data",
      },
    ],
  };
  return option;
};

export const createChartOp5 = (data) => {
  const rawData = data ?? [
    {
      time: "05-04",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-05",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-06",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-07",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-08",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-09",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
    {
      time: "05-10",
      val1: 0,
      val2: 0,
      val3: 0,
      val4: 0,
    },
  ];
  const dataset = {
    dimensions: ["time", "val1", "val2", "val3", "val4"],
    source: rawData,
  };

  const colors = [
    {
      name: "超时待处理",
      lineColor: "rgba(225, 98, 98, 1)",
      areaColor1: "rgba(225, 98, 98, 0.5)",
      areaColor2: "rgba(225, 98, 98, 0.0)",
    },
    {
      name: "超时已处理",
      lineColor: "rgba(116, 141, 237, 1)",
      areaColor1: "rgba(116, 141, 237, 0.5)",
      areaColor2: "rgba(116, 141, 237, 0)",
    },
    {
      name: "已完成",
      lineColor: "rgba(101, 234, 162, 1)",
      areaColor1: "rgba(101, 234, 162, 0.5)",
      areaColor2: "rgba(101, 234, 162, 0)",
    },
    {
      name: "待处理",
      lineColor: "rgba(254, 225, 134, 1)",
      areaColor1: "rgba(254, 225, 134, 0.5)",
      areaColor2: "rgba(254, 225, 134, 0)",
    },
  ];

  const axisLabel = {
    fontSize: 28,
    color: "rgba(203, 221, 242, 0.60)",
  };

  const series = Array.from({ length: 4 }, (_, index) => {
    const currentColor = colors[index];
    return {
      name: currentColor.name,
      type: "line",
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 0.6,
          colorStops: [
            {
              offset: 0,
              color: currentColor.areaColor1, // 0% 处的颜色
            },
            {
              offset: 1,
              color: currentColor.areaColor2, // 100% 处的颜色
            },
          ],
          global: false, // 缺省为 false
        },
      },
      lineStyle: {
        color: currentColor.lineColor,
      },
      itemStyle: {
        color: currentColor.lineColor,
      },
      // symbol: "none",
      symbol: "circle",
      symbolSize: 10,
      showSymbol: false,
      smooth: true,
    };
  });

  const option = {
    // backgroundColor: "#031a40",
    legend: {
      show: true,
      itemGap: 40,
      textStyle: {
        color: "rgba(203, 221, 242, 1)",
        fontSize: 32,
      },
    },
    tooltip: {
      trigger: "axis",
      padding: 22,
      axisPointer: {
        snap: true,
        type: "line",
        lineStyle: {
          color: "rgba(247, 249, 252, 0.30)", // 线的颜色
          width: 4, // 线宽
          type: "solid", // 线型
        },
      },
      backgroundColor: "rgba(27, 23, 21, 0.80)",
      borderWidth: 0,
      textStyle: {
        fontSize: 40,
        color: 'fff',
      },
    },
    grid: {
      left: 0,
      bottom: 0,
      containLabel: true,
      right: 0,
      top: "20%",
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false, //隐藏X轴刻度
      },
      axisLabel,
    },
    yAxis: {
      type: "value",
      name: "",
      axisLabel,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(223, 223, 223, 1)",
        },
      },
    },
    dataset,
    series,
  };
  return option;
};

export const createChartOp6 = (data) => {
  const rawData = data ?? [
    {
      date: "05-16",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-17",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-18",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-19",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-20",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-21",
      val1: 0,
      val2: 0,
      val3: 0,
    },
    {
      date: "05-22",
      val1: 1,
      val2: 0,
      val3: 0,
    },
  ];
  const dataset = {
    dimensions: ["date", "val1", "val2", "val3"],
    source: rawData,
  };

  const colors = [
    {
      name: "单人入廊",
      lineColor: "rgba(254, 225, 134, 1)",
    },
    {
      name: "人员跌倒",
      lineColor: "rgba(101, 234, 162, 1)",
    },
    {
      name: "未规范佩戴安全帽",
      lineColor: "rgba(116, 141, 237, 1)",
    },
  ];

  const axisLabel = {
    fontSize: 28,
    color: "rgba(203, 221, 242, 0.60)",
  };

  const series = Array.from({ length: 3 }, (_, index) => {
    const currentColor = colors[index];
    return {
      name: currentColor.name,
      type: "bar",
      barWidth: "12px",
      itemStyle: {
        color: currentColor.lineColor,
      },
    };
  });

  const option = {
    // backgroundColor: "#031a40",
    legend: {
      show: true,
      itemGap: 40,
      textStyle: {
        color: "rgba(203, 221, 242, 1)",
        fontSize: 32,
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        snap: true,
        type: "shadow",
        shadowStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 255, 255, 0)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(88, 147, 175, 0.7)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      padding: 22,
      backgroundColor: "rgba(27, 23, 21, 0.80)",
      borderWidth: 0,
      textStyle: {
        color: "#FFFFFF",
        fontSize: 40,
      },
    },
    grid: {
      left: 0,
      bottom: 0,
      containLabel: true,
      right: 0,
      top: "26%",
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false, //隐藏X轴刻度
      },
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
        interval: 0,
      },
    },
    yAxis: {
      type: "value",
      name: "次",
      nameTextStyle: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
        align: "center",
        padding: [0, 50, 10, 0],
      },
      axisLabel,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(223, 223, 223, 1)",
        },
      },
    },
    dataset,
    series,
  };
  return option;
};

export const createChartOp7 = (data) => {
  const rawData = data ?? [
    {
      name: "隐患",
      value: 0,
    },
    {
      name: "维修",
      value: 0,
    },
    {
      name: "告警",
      value: 0,
    },
    {
      name: "日常",
      value: 0,
    },
    {
      name: "保养",
      value: 0,
    },
    {
      name: "巡检",
      value: 0,
    },
  ];
  const dataset = {
    dimensions: ["name", "value"],
    source: rawData,
  };
  const option = {
    grid: {
      right: 0,
      bottom: 0,
      left: 0,
      top: "16%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        snap: true,
        type: "shadow",
      },
      formatter: function (params) {
        const item = params[0];
        return `${item.name}<br/>数量: ${item.value?.value} 次`;
      },
      padding: 22,
      backgroundColor: "rgba(27, 23, 21, 0.80)",
      borderWidth: 0,
      textStyle: {
        color: "#FFFFFF",
        fontSize: 40,
      },
    },
    xAxis: {
      type: "category",
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
    },
    yAxis: {
      type: "value",
      name: "次",
      nameTextStyle: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
        align: "center",
        padding: [0, 50, 10, 0],
      },
      axisLabel: {
        fontSize: 28,
        color: "rgba(203, 221, 242, 0.60)",
      },
      axisLine: {
        itemStyle: {
          color: "rgba(203, 221, 242, 0.30)",
        },
      },
    },
    dataset,
    series: [
      {
        type: "bar",
        barWidth: "20px",
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 255, 255, 1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(105, 193, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  };
  return option;
};
