<template>
  <div class="c-table">
    <div class="c-table-inner" :style="{ maxWidth: maxWidth, width: maxWidth }">
      <ul class="c-table-head">
        <li
          v-for="tit in titles"
          :key="tit.prop"
          class="th-cell"
          :class="['align-' + (tit.dir ?? dir)]"
          :style="{
            flex: getHeadCellWidth(tit),
          }"
        >
          {{ tit.label }}
        </li>
      </ul>
      <div
        class="c-table-body-wrap"
        :style="{
          maxHeight: maxHeight,
          height: maxHeight,
          overflowY: autoScroll ? 'hidden' : 'auto',
        }"
        @mouseenter.stop="controlScroll('paused')"
        @mouseleave.stop="controlScroll('running')"
      >
        <div
          v-if="tableData && tableData.length"
          class="c-table-body-inner"
          :class="[autoScroll ? 'scroll-up' : '']"
          :style="{
            animationDuration: getAnimationDuration(),
          }"
          ref="tableBodyInnerRef"
        >
          <ul class="c-table-body" :style="{ gap, padding: bodyPadding(gap) }">
            <li
              v-for="(row, rindex) in tableData"
              :key="rowKey ? row[rowKey] : rindex"
            >
              <ul
                class="c-table-row"
                :class="[
                  activeRowKey === (rowKey ? row[rowKey] : rindex)
                    ? 'row-active'
                    : '',
                ]"
                @click="handleItemClick(row, rindex)"
              >
                <li
                  v-for="(tit, cindex) in titles"
                  :key="
                    (row['transform_' + tit.prop] =
                      tit.transform?.(row[tit.prop]) || row[tit.prop])
                  "
                  class="td-cell shrink-0 truncate"
                  :class="['align-' + (tit.dir ?? dir)]"
                  :style="{
                    flex: getHeadCellWidth(tit),
                  }"
                  @click="handleCellClick(row, tit.prop, row[tit.prop], rindex, cindex)"
                >
                  <!-- <slot
                    :name="tit.prop"
                    :val="row[tit.prop]"
                    :row="row"
                    :prop-key="tit.prop"
                    :col-index="cindex"
                  >
                    <span class="truncate">
                      {{
                        (row["transform_" + tit.prop]+'').length > tit.maxLength
                          ? row["transform_" + tit.prop].slice(
                              0,
                              tit.maxLength
                            ) + "..."
                          : row["transform_" + tit.prop]
                      }}
                    </span>
                  </slot> -->
                  <slot
                    :name="tit.prop"
                    :val="row[tit.prop]"
                    :row="row"
                    :prop-key="tit.prop"
                    :col-index="cindex"
                  >
                    <el-tooltip
                      :content="formatVal(row['transform_' + tit.prop])"
                      placement="top"
                      effect="dark"
                      teleported
                    >
                      <span class="truncate">
                        {{
                          (formatVal(row["transform_" + tit.prop]) + "")
                            .length > tit.maxLength
                            ? formatVal(row["transform_" + tit.prop]).slice(
                                0,
                                tit.maxLength
                              ) + "..."
                            : formatVal(row["transform_" + tit.prop])
                        }}
                      </span>
                    </el-tooltip>
                  </slot>

                  <!-- <template v-if="tit.tooltip">
                    <div class="tooltip-content">
                      {{ tit.tooltip_content || row["transform_" + tit.prop] }}
                    </div>
                  </template> -->
                </li>
              </ul>
            </li>
          </ul>
          <ul
            class="c-table-body"
            :style="{ gap, padding: bodyPadding(gap) }"
            v-if="autoScroll"
          >
            <li
              v-for="(row, rindex) in tableData"
              :key="rowKey ? row[rowKey] : rindex"
            >
              <ul
                class="c-table-row"
                :class="[
                  activeRowKey === (rowKey ? row[rowKey] : rindex)
                    ? 'row-active'
                    : '',
                ]"
                @click="handleItemClick(row, rindex)"
              >
                <li
                  v-for="(tit, cindex) in titles"
                  :key="
                    (row['transform_' + tit.prop] =
                      tit.transform?.(row[tit.prop]) || row[tit.prop])
                  "
                  class="td-cell shrink-0 truncate"
                  :class="['align-' + (tit.dir ?? dir)]"
                  :style="{
                    flex: getHeadCellWidth(tit),
                  }"
                >
                  <slot
                    :name="tit.prop"
                    :val="row[tit.prop]"
                    :row="row"
                    :prop-key="tit.prop"
                    :col-index="cindex"
                  >
                    {{
                      (row["transform_" + tit.prop] + "").length > tit.maxLength
                        ? row["transform_" + tit.prop].slice(0, tit.maxLength) +
                          "..."
                        : row["transform_" + tit.prop]
                    }}
                  </slot>
                  <template v-if="tit.tooltip">
                    <div class="tooltip-content">
                      {{ tit.tooltip_content || row["transform_" + tit.prop] }}
                    </div>
                  </template>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div v-if="!tableData.length">
          <slot name="empty"> <div class="c-table-empty">暂无数据</div> </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

const props = defineProps({
  titles: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  dir: {
    type: String,
    default: "center",
  },
  maxHeight: {
    // 最大高度 出现滚动条
    type: String,
    default: "auto",
  },
  maxWidth: {
    // 最大宽度 出现滚动条
    type: String,
    default: "auto",
  },
  autoScroll: {
    // 是否自动滚动
    type: Boolean,
    default: false,
  },
  scrollSpeed: {
    // 单行滚动速度
    type: Number,
    default: 1,
  },
  order: {
    type: Boolean,
    default: false,
  },
  orderLabel: {
    type: String,
    default: "序号",
  },
  gap: {
    type: String,
    default: "2px",
  },
  rowKey: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["itemClick","cellClick"]);
const tableBodyInnerRef = ref(null);
const activeRowKey = ref("");
const handleCellClick = (row, prop, val, rowIndex, colIndex) => {
  emit("cellClick", { row, prop, val, rowIndex, colIndex });
};
// 获取表头单元格宽度
const getHeadCellWidth = (tit) =>
  `${tit.width || (tit.label + "").length || 1} `;

// 动画总时间
const getAnimationDuration = () =>
  ((props.tableData.length * props.scrollSpeed).toFixed(1) || 6) + "s";

// body padding
const bodyPadding = (gap) => (props.autoScroll ? `${gap} 0 0 ` : `${gap} 0 `);

// 控制动画滚动
const controlScroll = (type) => {
  const tableBodyInnerEl = tableBodyInnerRef.value;
  if (!tableBodyInnerEl) return;
  tableBodyInnerEl.style.animationPlayState = type;
};
const formatVal = (val) => {
  return val === null || val === undefined || val === "" ? "-" : val;
};

// 处理表格点击事件
const handleItemClick = (row, rindex) => {
  const itemActiveVal = props.rowKey ? row[props.rowKey] : rindex;
  // if (activeRowKey.value === itemActiveVal) return;
  activeRowKey.value = itemActiveVal;
  emit("itemClick", { row, rindex });
};
</script>

<style lang="less" scoped>
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.c-table {
  width: 100%;
  overflow-x: auto;
  color: white;

  &-head {
    display: flex;
    .th-cell {
      position: relative;
      display: flex;
      align-items: center;
    }
  }
  &-body-wrap {
    overflow-y: auto;
  }
  &-body {
    display: flex;
    flex-direction: column;

    .td-cell {
      position: relative;
      display: flex;
      align-items: center;
      .tooltip-content {
        display: none;
        position: absolute;
        bottom: 100%;
        background-color: #999;
      }

      &:hover {
        .tooltip-content {
          display: block;
        }
      }
    }

    .row-active {
      // background: red;
    }
  }
  &-row {
    display: flex;
    flex-direction: row;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  &-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
  }

  // 文字对齐方式
  .align-left {
    justify-content: flex-start;
  }
  .align-center {
    justify-content: center;
  }
  .align-right {
    justify-content: flex-end;
  }

  .scroll-up {
    animation: scroll-up 6s linear infinite;
  }

  @keyframes scroll-up {
    0% {
      transform: translateY(0) translateZ(0);
    }
    100% {
      transform: translateY(-50%) translateZ(0);
    }
  }
}
</style>
