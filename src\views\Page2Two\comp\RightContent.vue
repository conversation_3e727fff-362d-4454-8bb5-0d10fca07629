<template>
  <div class="text-[40px]">
    <PanelTitle
      v-loading="loading1"
      :element-loading-text="msg"
      title="告警详情列表2"
    >
      <div class="h-[990px] px-[56px]">
        <MySwitch
          :options="pageStaticData.section1.ops"
          val="k1"
          class="justify-end pt-[106px]"
          @switchChange="
            (item) => handleSwitchChange(item, 'AlarmgetdailyAlarmListInfo')
          "
        />
        <MyTable
          :titles="pageStaticData.section2.titles"
          :table-data="tableData"
          max-height="550px"
          :autoScroll="tableData.length > 5"
          gap="18px"
          class="mt-[22px] p2-tb1"
          @itemClick="pageHandlers.section2ClickHandler"
        >
          <template #k1="{ val }">
            <span
              class="inline-block bg-[#CBDDF2] w-[48px] h-[48px] cus-flex-center text-[#FFFFFF]"
            >
              {{ val }}
            </span>
          </template>
          <template #k2="{ val }">
            <div class="truncate w-[300px]">
              {{ val }}
            </div>
          </template>
          <template #k6="{ val }">
            <span
              class="inline-block bg-[#CBDDF2] w-[48px] h-[48px] cus-flex-center"
              :class="pageStaticData.section2.handleWaning(val).class"
            >
              {{ pageStaticData.section2.handleWaning(val).label }}
            </span>
          </template>
        </MyTable>
        <div class="flex justify-center ccc">
          <el-pagination
            v-if="paginationConfig.total > 0"
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            :page-sizes="paginationConfig.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @size-change="(size) => sizeChange(size, paginationConfig)"
            @current-change="(page) => currentChange(page, paginationConfig)"
            class="scale-[2.3] mt-[60px]"
            locale="zh-cn"
          />
        </div>
      </div>
      <Refresh @click="alarmList" class="right-[100px] top-[30px]" />
    </PanelTitle>
    <PanelTitle
      v-loading="loading2"
      title="近七天自动巡检报告列表"
      class="mt-[30px]"
      :element-loading-text="msg"
    >
      <div class="h-[804px] pt-[126px] px-[56px]">
        <MyTable
          :titles="pageStaticData.section3.titles"
          :table-data="pageRefData.section3.tableData"
          gap="0"
          tStyle="style2"
          dir="left"
          class="cus"
          @itemClick="pageHandlers.section3ClickHandler"
          :autoScroll="pageRefData.section3.tableData.length > 5"
          max-height="450px"
        >
          <template #k1="{ val }">
            <span
              class="inline-block w-[48px] h-[48px] cus-flex-center text-[#FFFFFF] t2-order-bg"
            >
              {{ val }}
            </span>
          </template>
        </MyTable>
        <div class="flex justify-center ccc">
          <el-pagination
            v-if="paginationConfig1.total"
            v-model:current-page="paginationConfig1.currentPage"
            v-model:page-size="paginationConfig1.pageSize"
            :page-sizes="paginationConfig1.pageSizes"
            size="large"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig1.total"
            @size-change="(size) => sizeChange(size, paginationConfig1)"
            @current-change="(page) => currentChange(page, paginationConfig1)"
            class="scale-[2] mt-[40px]"
            locale="zh-cn"
          />
        </div>
      </div>
      <Refresh @click="getData('l2')" class="right-[100px] top-[30px]" />
    </PanelTitle>
    <PanelTitle v-loading="loading3" title="管廊可视化分析" class="mt-[44px]">
      <div class="h-[858px] px-[56px] pt-[100px]">
        <div class="w-full h-[744px] relative cus-flex-center">
          <div
            class="inset-0 absolute bg-[url('@/assets/imgs/page2/p41.png')] cus-bg-full"
          ></div>
          <div
            class="w-[1322px] h-[744px] absolute cus-bg-full"
            :style="{ backgroundImage: `url(${getImg(img)})` }"
          ></div>
        </div>
      </div>
      <!-- <Refresh @click="getData('l3')" class="right-[100px] top-[30px]" /> -->
    </PanelTitle>

    <!-- 巡检列表弹窗 -->
    <Teleport to="#app-main">
      <My-Tip
        stl="right-[2300px] top-[1480px] bg-inherit p2-ct1"
        v-model:visible="pageRefData.tips.tip1.visible"
        close
        :auto-close="false"
      >
        <div
          class="text-[48px] w-[1443px] h-[659px] px-[50px] pt-[40px] pb-[90px] cus-bg-full bg-[url('@/assets/imgs/page2/p123.png')]"
        >
          <div
            class="flex w-[301px] h-[56px] cus-bg-full bg-[url('@/assets/imgs/page2/p124.png')]"
          ></div>
          <div class="text-[38px] text-[#CBDDF2FF]">
            <div class="mt-[48px]">
              <C-Table
                :titles="pageRefData.tips.tip1.titles"
                :table-data="pageRefData.tips.tip1.detail"
              >
                <template #k3="{ val, row }">
                  <span
                    class="text-[#F75959FF] cus-use-click"
                    @click="pageRefData.tips.tip1.viewItemHandle(row)"
                    >{{ val }}</span
                  >
                </template>
                <template #k5="{ val, row }">
                  <span
                    class="text-[#13ECFFFF] cus-use-click"
                    @click="pageRefData.tips.tip1.viewItemHandle(row)"
                    >{{ val }}</span
                  >
                </template>
              </C-Table>
            </div>
          </div>
        </div>
      </My-Tip>
    </Teleport>

    <!-- 仅七天自动巡检报告详情弹窗 -->
    <Teleport to="#app-main">
      <My-Tip
        stl="right-[2800px] top-[1280px] p2-ct2"
        v-model:visible="pageRefData.tips.tip2.visible"
        close
        show-mask
      >
        <div class="text-[48px] w-[2140px] px-[90px] py-[10px] pb-[90px]">
          <div
            class="flex w-[240px] h-[78px] cus-bg-full bg-[url('@/assets/imgs/comp/p55.png')] mx-auto"
          ></div>
          <div class="text-[38px] text-[#CBDDF2FF]">
            <div class="flex items-center">
              <span>巡检时间：{{ pageRefData.tips.tip2.inspectionTime }}</span>
              <span class="ml-auto">报告结果：</span
              ><span class="text-[#F75959FF]">{{
                pageRefData.tips.tip2.result
              }}</span>
            </div>
            <div
              class="h-[2px] cus-bg-full bg-[url('@/assets/imgs/page2/p117.png')] mt-[20px]"
            ></div>
            <MyTipSectionTitle> 廊内设备状态 </MyTipSectionTitle>
            <div class="mt-[48px]">
              <C-Table
                :titles="pageRefData.tips.tip2.titles"
                :table-data="pageRefData.tips.tip2.detail"
                max-height="900px"
              >
                <template #k2="{ val }">
                  <span class="text-[#F75959FF]" v-if="val === '是'">是</span>
                  <span class="text-[#FFFFFF]" v-else>否</span>
                </template>
              </C-Table>
            </div>
            <MyTipSectionTitle> 告警图片 </MyTipSectionTitle>
            <div class="mt-[48px] overflow-y-hidden">
              <div class="flex gap-x-[40px]">
                <img
                  v-for="imgUrl in pageRefData.tips.tip2.imgs"
                  :src="imgUrl"
                  :key="imgUrl"
                  class="h-[254px] shrink-0"
                />
              </div>
            </div>
          </div>
        </div>
      </My-Tip>
    </Teleport>

    <!-- 告警详情 -->
    <My-Tip2
      class="right-[2228px] top-[678px]"
      v-model:visible="pageRefData.tips.tip3.visible"
      @close-tip="pageRefData.tips.tip3.closeHandle"
    >
      <div class="flex flex-col gap-y-[24px]">
        <div
          v-for="item in pageRefData.tips.tip3.detail"
          :key="item.label"
          class="flex items-center odd:bg-[url('@/assets/imgs/page1/p101.png')] even:bg-[url('@/assets/imgs/page1/p102.png')] w-[716px] h-[64px] cus-bg-full"
        >
           <span class="w-[200px] shrink-0">{{ item.label }}：</span>
          <span v-if="item.label !== '图片'" class="truncate">
            <template v-if="item.label === '告警等级'">
              {{
                item.value !== null && item.value !== undefined
                  ? handleWaning(item.value)?.label
                  : "暂无数据"
              }}
            </template>
            <template v-else>
              {{
                item.value !== null && item.value !== undefined && item.value !== ''
                  ? item.value
                  : "暂无数据"
              }}
            </template>
          </span>
          <span v-else @click="handleImg(item)">
            {{ item.value ? "查看告警图片" : "暂无数据" }}
          </span>
        </div>
      </div>
      <div class="flex mt-[24px] items-center justify-around">
        <div
          class="cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center cus-use-click"
          v-for="item in pageRefData.tips.tip3.opts"
          :key="item"
          @click="() => item.cb?.()"
        >
           <template v-if="item.label == '告警定位'">
            <el-tooltip
              v-if="pageRefData.tips.tip3.detail.status == 'no'"
              content="当前设备不能定位"
              placement="top"
              effect="dark"
              teleported
            >
              <div
                :style="{ cursor: 'not-allowed', opacity: 0.5 }"
                
              >{{ item.label }}</div>
            </el-tooltip>
            <span v-else>{{ item.label }}</span>
          </template>
          <template v-else>
            <span>{{ item.label }}</span>
          </template>
        </div>
      </div>
    </My-Tip2>
    <!-- 上报意见填写 -->
    <My-Tip2
      class="right-[3201px] top-[678px]"
      stl="t2"
      title_class="tt2"
      v-model:visible="pageRefData.tips.tip4.visible"
    >
      <div>
        <div class="mx-[18px]">
          <textarea
            v-model="pageRefData.tips.tip4.detail.text"
            class="w-full h-[310px] outline-none bg-transparent bg-gradient-to-b from-[#1482CD66] to-[#1685CD33]"
          ></textarea>
        </div>
        <div class="flex mt-[24px] items-center mr-[20px]">
          <div
            v-for="item in pageRefData.tips.tip4.opts"
            :key="item"
            class="cus-use-click cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center first:ml-auto last:ml-[24px]"
            @click="() => item.cb?.()"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </My-Tip2>

    <!-- 处理意见填写 -->
    <My-Tip2
      class="right-[3201px] top-[678px]"
      stl="t3"
      title_class="tt3"
      v-model:visible="pageRefData.tips.tip5.visible"
    >
      <div>
        <div class="mx-[18px]">
          <textarea
            v-model="pageRefData.tips.tip5.detail.text"
            class="w-full h-[310px] outline-none bg-transparent bg-gradient-to-b from-[#1482CD66] to-[#1685CD33]"
          ></textarea>
        </div>
        <div class="flex mt-[24px] items-center mr-[20px]">
          <div
            v-for="item in pageRefData.tips.tip5.opts"
            :key="item"
            class="cus-use-click cus-bg-full w-[222px] h-[64px] bg-[url('@/assets/imgs/page1/p103.png')] last:bg-[url('@/assets/imgs/page1/p104.png')] cus-flex-center first:ml-auto last:ml-[24px]"
            @click="() => item.cb?.()"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </My-Tip2>
    <el-dialog v-model="dialogVisible" width="40%" title="告警图片">
      <div class="flex justify-center my-[0px]">
        <el-image class="w-full" style="margin: auto" :src="imgUrl" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  AlarmgetdailyAlarmListInfo,
  getSevenDayList,
  insertZhjkAlarmReport,
  addZhjkAlarmInfo,
  getInspectionRecordList,
} from "@/axios/apis/page2";
import pollingManager from "@/utils/pollingManager";
import patchReq from "@/utils/patchReq";
import request from "@/axios/request";
import {
  onMounted,
  ref,
  markRaw,
  reactive,
  watch,
  computed,
  onUnmounted,
} from "vue";
import Refresh from "@/components/my-ui/Refresh.vue";
import { getImg } from "@/utils/assets";
import { useStore } from "vuex";
import { getJSONFromStorage,handleWaning } from "@/utils";

const props = defineProps({
  // props
});
const currentTop = ref("zhc");
const msg = ref("");
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const paginationConfig = ref({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 15, 30, 50],
  total: 0,
  disabled: false,
});
const dialogVisible = ref(false);
const paginationConfig1 = ref({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 15, 30, 50],
  total: 0,
  disabled: false,
});
const deviceCode = ref("");
const img = computed(() => {
  return `fenqu/${store.state.pageTwoCode[1]}.png`;
});
const pageStaticData = {
  section1: {
    ops: [
      {
        label: "综合舱",
        key: "k1",
        en: "zhc",
      },
      {
        label: "电力舱",
        key: "k2",
        en: "dlc",
      },
      {
        label: "进风口",
        key: "k3",
        en: "jfk",
      },
      {
        label: "排风口",
        key: "k4",
        en: "pfk",
      },
      {
        label: "人员出入口",
        key: "k5",
        en: "crk",
      },
    ],
  },
  section2: {
    titles: [
      {
        label: "序号",
        prop: "k1",
      },
      {
        label: "告警设备",
        prop: "k2",
      },
      {
        label: "告警区域",
        prop: "k3",
        width: 5,
      },
      {
        label: "告警详情",
        prop: "k4",
      },
      {
        label: "告警时间",
        prop: "k5",
      },
      {
        label: "告警等级",
        prop: "k6",
        width: 2.6,
      },
    ],
    handleWaning: (val) => {
      const map = new Map([
        ["0", { label: "高", class: "warning-heigh warning-base" }],
        ["1", { label: "中", class: "warning-medium warning-base" }],
        ["2", { label: "低", class: "warning-lower warning-base" }],
      ]);
      return (
        map.get(val + "") ?? {
          label: "低",
          class: "warning-lower warning-base",
        }
      );
    },
  },
  section3: {
    titles: [
      {
        label: "序号",
        prop: "k1",
        dir: "center",
        width: 1,
      },
      {
        label: "巡检地点",
        prop: "k2",
        width: 2,
        dir: "center",
      },
      {
        label: "巡检人员",
        prop: "k3",
        width: 1.6,
        dir: "center",
      },
      {
        label: "报告名称",
        prop: "k4",
        width: 3,
        dir: "center",
      },
      {
        label: "告警时间",
        prop: "k5",
        width: 3.6,
      },
    ],
  },
};

const pageRefData = ref({
  tips: {
    tip1: {
      visible: false,
      detail: [
        {
          k1: "1",
          k2: "2024-03-19 15:15:10",
          k3: "异常",
          k4: "存在1个设备告警",
          k5: "查看",
        },
        {
          k1: "2",
          k2: "2024-03-19 15:15:10",
          k3: "异常",
          k4: "存在2个设备告警",
          k5: "查看",
        },
      ],
      titles: markRaw([
        {
          label: "序号",
          prop: "k1",
          width: 1,
        },
        {
          label: "报告时间",
          prop: "k2",
          width: 2,
        },
        {
          label: "报告状态",
          prop: "k3",
          width: 1,
        },
        {
          label: "报告结果",
          prop: "k4",
          width: 1,
        },
        {
          label: "操作",
          prop: "k5",
          width: 1,
        },
      ]),
      viewItemHandle: (item) => {
        pageRefData.value.tips.tip2.visible = true;
      },
    },
    tip2: {
      visible: false,
      inspectionTime: "2024-08-19 15:20:00",
      result: "异常",
      imgs: [],
      detail: [
        {
          k1: "1分区电力舱通信话站副机01",
          k2: "是",
          k3: "2024-03-19 15:15:10",
        },
        {
          k1: "2分区电力舱通信话站副机02",
          k2: "是",
          k3: "2024-03-19 15:15:10",
        },
        {
          k1: "3分区电力舱通信话站副机03",
          k2: "否",
          k3: "2024-03-19 15:15:10",
        },
        {
          k1: "4分区电力舱通信话站副机04",
          k2: "是",
          k3: "2024-03-19 15:15:10",
        },
        {
          k1: "5分区电力舱通信话站副机05",
          k2: "是",
          k3: "2024-03-19 15:15:10",
        },
      ],
      titles: markRaw([
        {
          label: "设备名称",
          prop: "k1",
          dir: "left",
          width: 1,
        },
        {
          label: "是否异常",
          prop: "k2",
          width: 1,
        },
        {
          label: "告警区域",
          prop: "k3",
          dir: "left",
          width: 1,
        },
      ]),
    },
    tip3: {
      visible: false,
      index: 0,
      currentid: "",
      closeHandle() {
        const tips = pageRefData.value.tips;
        tips.tip4.visible = false;
        tips.tip5.visible = false;
      },
      detail: markRaw([
        {
          label: "设备编码",
          prop: "equipmentCode",
          value: "",
        },
        {
          label: "设备名称",
          prop: "equipmentName",
          value: "01R01热力舱排风机",
        },
        {
          label: "设备位置",
          prop: "road",
          value: "北辰东道/1分区/热力舱",
        },
        {
          label: "告警分类",
          prop: "alarmClassify",
          value: "环控系统",
        },
        {
          label: "告警等级",
          prop: "alarmLevel",
          value: "高",
        },
        {
          label: "告警描述",
          prop: "alarmDescription",
          value: "异常",
        },
        {
          label: "告警时间",
          prop: "alarmTime",
          value: "2024-04-17 16:00",
        },
        {
          label: "图片",
          prop: "img",
          value: "",
        },
      ]),
      opts: [
        {
          label: "上报",
          cb() {
            const tips = pageRefData.value.tips;
            const cShow = tips.tip4.visible;
            tips.tip4.visible = !cShow;

            if (tips.tip5.visible && !cShow) {
              tips.tip5.visible = false;
            }
          },
        },
        {
          label: "处理",
          cb() {
            const tips = pageRefData.value.tips;
            const cShow = tips.tip5.visible;
            tips.tip5.visible = !cShow;

            if (tips.tip4.visible && !cShow) {
              tips.tip4.visible = false;
            }
          },
        },
        // {
        //   label: "告警定位",
        //   disabled: true,
        //   cb() {
        //     const tips = pageRefData.value.tips;
        //     const ids = [
        //       "1Z15EOX01",
        //       "1Z15ETH01",
        //       "1Z15ETH02",
        //       "1Z15ECH401",
        //       "1D01SCAM01",
        //       "1D01SCAM03",
        //       "1D02SCAM01",
        //     ];
        //     const params = {
        //       type: "web_device_status__change",
        //       data: {
        //         text: deviceCode.value,
        //       },
        //     };
        //     sendToUE5(params);
        //     pageRefData.value.tips.tip3.visible = false
        //   },
        // },
      ],
    },
    tip4: {
      visible: false,
      detail: {
        text: "",
      },
      opts: [
        {
          label: "取消",
          cb() {
            pageRefData.value.tips.tip4.visible = false;
          },
        },
        {
          label: "确认",
          cb() {
            const tips = pageRefData.value.tips;
            const cid = tips.tip3.currentid;
            const reason = tips.tip4.detail.text;
            insertZhjkAlarmReport({
              alarmId: cid,
              reason: reason,
            }).then((res) => {
              tips.tip4.detail.text = "";
              tips.tip4.visible = false;
              res.code == "00000"
                ? ElMessage.success({
                    message: "处理意见提交成功",
                  })
                : ElMessage.error({
                    message: res.message,
                  });
            });
          },
        },
      ],
    },
    tip5: {
      visible: false,
      detail: {
        text: "",
      },
      opts: [
        {
          label: "取消",
          cb() {
            pageRefData.value.tips.tip5.visible = false;
          },
        },
        {
          label: "确认",
          cb() {
            const tips = pageRefData.value.tips;
            const cid = tips.tip3.currentid;
            const reason = tips.tip5.detail.text;
            addZhjkAlarmInfo({
              alarmId: cid,
              reason: reason,
            }).then((res) => {
              tips.tip5.detail.text = "";
              tips.tip5.visible = false;
              res.code == "00000"
                ? ElMessage.success({
                    message: "处理意见提交成功",
                  })
                : ElMessage.error({
                    message: res.message,
                  });
            });
          },
        },
      ],
    },
  },
  section2: {
    tableData: markRaw(
      Array.from({ length: 5 }, (_, index) => ({
        k1: index + 1,
        k2: "温感探头",
        k3: "规划一街综合舱1区",
        k4: "探头故障",
        k5: "2024-03-19 15:15:10",
        k6: index,
      }))
    ),
  },
  section3: {
    tableData: markRaw(
      Array.from({ length: 5 }, (_, index) => ({
        k1: index + 1,
        k2: "规划一街综合舱1区",
        k3: "小张",
        k4: "报告名称1",
        k5: "2024-03-19 15:15:10",
      }))
    ),
  },
  section9: "page2-p121",
});
const imgUrl = ref("");
const handleImg = (item) => {
  if (!item.value) return;
  imgUrl.value = item.value;
  dialogVisible.value = true;
};
const tableData = ref([]);
const getTableData = async (flag, params) => {
  flag && (loading1.value = true);
  try {
    const _params = {
      current: 1,
      query: {
        cabinId: "",
        beginTime: "",
        endTime: "",
        handleStatus :0,
        code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
      },
      size: 10,
      ...params,
    };
    const res = await request(
      "/admin/pipe/monitor/alarm/findZhjkAlarmInfo",
      _params,
      "POST"
    );
    paginationConfig.value.total = res.data.total;
    console.log(res, "res");

    const data = res?.data?.records || [];
    msg.value = res.code == "00000" ? "请求成功" : "请求失败";
    tableData.value = data.map((i, index) => {
      return {
        ...i,
        k1: index + 1,
        k2: i.equipmentName,
        k3: i.partitionArea ?? "",
        k4: i.alarmDescription,
        k5: i.alarmTime,
        k6: i.alarmLevel ?? "",
        img: i.imgUrl,
      };
    });
  } catch (error) {
    msg.value = "请求失败";
  } finally {
    loading1.value = false;
  }
};
let task = () =>
  getTableData(true, {
    current: paginationConfig.value.currentPage,
    size: paginationConfig.value.pageSize,
  });
onMounted(() => {
  getTableData(false, {
    current: 1,
    size: 10,
  });
  pollingManager.register(task);
  pollingManager.register(getSevenList);
});
onUnmounted(() => {
  pollingManager.unregister(task);
  pollingManager.unregister(getSevenList);
});
const currentChange = (page, paginationConfig) => {
  console.log(paginationConfig1.value);

  paginationConfig.currentPage = page;
};
const sizeChange = (size, paginationConfig) => {
  console.log(paginationConfig1.value);

  paginationConfig.pageSize = size;
};
const store = useStore();
const apisMap = new Map([
  // [
  //   "AlarmgetdailyAlarmListInfo",
  //   {
  //     api_pro: AlarmgetdailyAlarmListInfo,
  //     params: {
  //       code: "tonghenanlu_fenqu10_zhc",
  //     },
  //     callback(handledData) {
  //       pageRefData.value.section2.tableData = markRaw(handledData);
  //     },
  //   },
  // ],
  [
    "getSevenDayList",
    {
      api_pro: getSevenDayList,
      params: {
        code:
          "tonghenanlu_fenqu" +
          store.state.pageTwoCode[1] +
          "_" +
          store.state.pageTwoCode[0],
        current: paginationConfig1.value.currentPage,
        size: paginationConfig1.value.pageSize,
      },
      callback(handledData) {
        paginationConfig1.value.total = handledData.total;
        pageRefData.value.section3.tableData = markRaw(handledData);
      },
    },
  ],
]);
const alarmList = () => {
  const [code = "zhc", num = 1] = store.state.pageTwoCode;
  getTableData(true, {
    current: paginationConfig.value.currentPage,
    size: paginationConfig.value.pageSize,
    query: {
      code: "tonghenanlu_fenqu" + num + "_" + currentTop.value,
      handleStatus :0,
    },
  });
};
watch(
  () => [
    paginationConfig.value.currentPage,
    paginationConfig.value.pageSize,
    currentTop.value,
  ],
  alarmList
);
const getSevenList = async () => {
  try {
    loading2.value = true;
    const res = await getSevenDayList({
      code:
        "tonghenanlu_fenqu" +
        store.state.pageTwoCode[1] +
        "_" +
        store.state.pageTwoCode[0],
      current: paginationConfig1.value.currentPage,
      size: paginationConfig1.value.pageSize,
    });
    pageRefData.value.section3.tableData = res;
    paginationConfig1.value.total = res.total;
  } catch (error) {
    msg.value = "请求错误";
  } finally {
    loading2.value = false;
  }
};
watch(
  () => store.state.pageTwoCode,
  async () => {
    const [code = "zhc", num = 1] = store.state.pageTwoCode;
    alarmList();
    getSevenList();
  }
);
const getData = async (type) => {
  const obj = {
    l1: loading1,
    l2: loading2,
    l3: loading3,
  };
  const loading = obj[type] || {};
  loading.value = true;
  await patchReq([...apisMap.values()]);
  loading.value = false;
};
const pageHandlers = {
  section2ClickHandler({ row, rindex }) {
    let devices = getJSONFromStorage("devices");
    const tips = pageRefData.value.tips;
    const tData = tips.tip3.detail.map((item) => ({
      ...item,
      value: row ? row[item.prop] : "暂无数据",
    }));
    const info = tData.find((i) => i.prop == "equipmentCode");
    if (devices.length > 0) {
      const device = devices.find((i) => i == info?.value);
      if (device) {
        tData.status = "yes";
      } else {
        tData.status = "no";
      }
    }
    if (info) {
      deviceCode.value = info.value;
    }
    tips.tip3.detail = markRaw(tData);
    console.log(tips.tip3.detail);

    pageRefData.value.tips.tip3.visible = true;
    tips.tip3.index = rindex;
    tips.tip3.currentid = row.id;
  },
  section3ClickHandler({ row = {} }) {
    const tip1 = pageRefData.value.tips.tip1;
    const rowDetail = row.detail || {};

    tip1.detail = [
      {
        k1: "1",
        k2: rowDetail.createTime,
        k3: rowDetail.result,
        k4: rowDetail.reportResult,
        k5: "查看",
        id: rowDetail.id,
      },
    ];
    pageRefData.value.tips.tip1.visible = true;
    const id = row.detail?.id;
    if (id) {
      getInspectionRecordList({ id }).then((res) => {
        const tip2 = pageRefData.value.tips.tip2;
        tip2.inspectionTime = res.inspectionTime;
        tip2.result = res.result;

        const att = res.att || [];
        tip2.detail.length = 0;
        tip2.imgs.length = 0;
        tip2.detail = markRaw(
          att.map((item) => {
            if (item.exceptionImg) {
              tip2.imgs.push(item.exceptionImg);
            }

            return {
              k1: item.deviceName,
              k2: item.isException,
              k3: rowDetail.areaName,
            };
          })
        );
      });
    }
  },
};

// 页面switch切换
const handleSwitchChange = async (item, apiName) => {
  currentTop.value = item.en;
  const apiConfig = apisMap.get(apiName);
  if (apiConfig) {
    console.log(item.key);
    const res = await apiConfig.api_pro({
      code: item.key,
    });
    apiConfig.callback(res);
  }
};

onMounted(() => {
  getData();
});
watch(
  () => [paginationConfig1.value.currentPage, paginationConfig1.value.pageSize],
  getSevenList
);
</script>

<style lang="less">
.p2-ct1 {
  .e-close {
    top: 10px;
    right: 0;
  }

  // background-color: red;
  padding: 0;

  .c-table {
    .c-table-head {
      color: rgba(203, 221, 242, 1);
      background-image: initial !important;
    }

    .c-table-body {
      li {
        background-image: initial !important;
      }
    }
  }
}

.p2-ct2 .c-table {
  .c-table-head {
    background-image: url("@/assets/imgs/page2/p119.png");
    color: #94dcefff;
    font-size: 36px;
  }

  .c-table-body {
    color: #cbddf2ff;

    .c-table-row {
      height: 92px;
    }

    > li:nth-child(odd) {
      background-image: url("@/assets/imgs/page2/p120.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}

.p2-tb1 .c-table-row {
  pointer-events: auto;

  &:hover {
    cursor: pointer;
  }
}
</style>

<style lang="less" scoped>
.warning-base {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 124px;
  height: 64px;
  color: white;
}

.warning-heigh {
  background-color: rgba(255, 34, 0, 0.15);
  color: #ff7184;
}

.warning-medium {
  background-color: rgba(255, 153, 0, 0.15);
  color: #fdff8b;
}

.warning-lower {
  background-color: rgba(0, 209, 255, 0.15);
  color: #00d1ff;
}

.t2-order-bg {
  background-image: linear-gradient(to top right, #d6f3ffff, #a0d2ff4a 40%);
}

:deep(.cus .c-table-row) {
  &:hover {
    cursor: pointer;
  }
}

.page2-p121 {
  background-image: url("@/assets/imgs/page2/p121.png");
}

.disabled {
  filter: grayscale(0.7);
}
.ccc {
  :deep(.el-pagination) {
    color: #fff;

    .el-input__inner,
    .el-pagination__goto,
    .el-pagination__total,
    .el-pagination__classifier {
      color: #fff;
    }

    .el-input__wrapper {
      background-color: #49779299;
    }

    .number,
    .btn-prev,
    .more,
    .btn-next {
      background-color: #49779299;
      color: #fff;
      margin-left: 10px;
    }

    .is-active {
      background-color: #30b2ff99;
    }
  }

  :deep(.el-cascader) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-input__suffix-inner {
      color: white;
    }
  }

  :deep(.el-select) {
    background-color: transparent;

    .el-input {
      background-color: transparent;
    }

    .el-input__wrapper {
      background-color: rgba(120, 196, 238, 0.533);
    }

    .el-input__inner {
      color: white;

      &::placeholder {
        color: white;
      }
    }

    .el-select__icon {
      color: white;
    }

    .el-select__tags {
      top: 20%;

      .el-tag--info {
        background-color: #244c69;
        color: white;
      }
    }
  }
}
</style>
