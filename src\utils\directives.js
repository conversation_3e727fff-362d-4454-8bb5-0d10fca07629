// 优化UE 内嵌网页时的输入问题，不能完全解决
const resetFocus = {
  mounted(el) {
    const inputEl =
      el instanceof HTMLTextAreaElement ? el : (el.querySelector("input") || el.querySelector("textarea")); // 取第一个input元素
    if (!inputEl) return;
    const tCodes = [8, 13, 32]; // 按键码
    inputEl.addEventListener("keydown", (e) => {
      const keyCode = e.keyCode;
      if (tCodes.includes(keyCode)) {
        console.log("reset focus ue 输入优化");
        inputEl.blur();
        inputEl.focus();
      }
    });
  },
};

export { resetFocus };
