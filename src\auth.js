import { getToken, reRequestToken } from "@/axios/api";

export async function setToken() {
  if (window._apiToken) {
    return window._apiToken;
  }
  const apiToken = await getToken();
  const delay = 1000 * 60 * 60 * 1.92; // 1.92 hours  两个小时以内需要重新请求token
  console.log(apiToken,'设置token');
  
  if (apiToken) {
    setInterval(() => {
      reRequestToken();
    }, delay);
  }
  return (window._apiToken = apiToken);
}

