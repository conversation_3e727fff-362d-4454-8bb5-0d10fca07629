<template>
  <div class="absolute bg cus-bg-full" :class="[stl]" v-show="visible">
    <div
      class="title cus-bg-full absolute left-[38px] top-[16px]"
      :class="[title_class]"
    ></div>
    <div
      class="absolute top-[2px] right-[12px] bg-[url('@/assets/imgs/comp/p54.png')] w-[44px] h-[50px] cus-bg-full scale-[1.2] z-10 hover:cursor-pointer"
      v-show="close"
      @click="closeTipHandle"
    ></div>
    <div class="mt-[126px] ml-[48px] mr-[26px]">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { onClickOutside } from "@vueuse/core";
import { ref } from "vue";
const target = ref(null);

const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  close: {
    type: Boolean,
    default: true,
  },
  stl: {
    default: String,
    default: "t1",
  },
  title_class: {
    default: String,
    default: "tt1",
  },
});
const emit = defineEmits(["update:visible", "close-tip"]);

onClickOutside(target, (event) => {
  // 点击弹窗外部关闭弹窗
  emit("update:visible", false);
  emit("close-tip");
});

const closeTipHandle = () => {
  console.log("点击关闭按钮");
  emit("update:visible", false);
  emit("close-tip");
};
</script>

<style lang="less" scoped>
.t1 {
  width: 790px;
  height: 938px;
  background-image: url("@/assets/imgs/page1/p99.png");
}
.tt1 {
  width: 240px;
  height: 78px;
  background-image: url("@/assets/imgs/page1/p100.png");
}

.t2 {
  width: 792px;
  height: 592px;
  background-image: url("@/assets/imgs/page1/p105.png");
}
.tt2 {
  width: 364px;
  height: 78px;
  background-image: url("@/assets/imgs/page1/p106.png");
}

.t3 {
  width: 792px;
  height: 592px;
  background-image: url("@/assets/imgs/page1/p108.png");
}
.tt3 {
  width: 364px;
  height: 78px;
  background-image: url("@/assets/imgs/page1/p107.png");
}
</style>
