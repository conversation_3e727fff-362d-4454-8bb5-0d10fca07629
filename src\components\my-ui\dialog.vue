<template>
  <div
    class="w-[1138px] h-[436px] cus-bg-full bg-[url('@/assets/imgs/comp/p61.png')] mt-[112px] px-[194px] pt-[52px]"
  >
    <div class="flex items-center">
      <svg
        t="1720162498472"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="1993"
        width="96"
        height="96"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          p-id="1994"
          fill="#ffffff"
        ></path>
        <path
          d="M512 688m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z"
          p-id="1995"
          fill="#ffffff"
        ></path>
        <path
          d="M488 576h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
          p-id="1996"
          fill="#ffffff"
        ></path>
      </svg>
      <span
        class="ml-[20px] text-[64px] text-white tracking-[6px] font-medium"
        >{{ title }}</span
      >
    </div>
    <div
      class="tracking-[4px] text-[52px] flex justify-between text-white mt-[140px]"
    >
      <div
        class="w-[236px] h-[88px] cus-bg-full bg-[url('@/assets/imgs/comp/p62.png')] cus-flex-center cus-use-click"
        @click="$emit('cancel')"
      >
        取消
      </div>
      <div
        class="w-[236px] h-[88px] cus-bg-full bg-[url('@/assets/imgs/comp/p63.png')] cus-flex-center cus-use-click"
        @click="$emit('confirm')"
      >
        确认
      </div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["cancel", "confirm"]);
</script>
